-- =====================================================
-- Sapphire Gaming Commands Script
-- Comprehensive admin command system
-- =====================================================

ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

print('^3[Sapphire Gaming Commands]^0 Loading admin command system...')

-- =====================================================
-- CONFIGURATION
-- =====================================================

-- Permission groups
local Config = {
    -- Groups that can use high-level admin commands (setgroup, ban, etc.)
    AdminGroups = { 'cl', 'leadership', 'god' },
    
    -- Groups that can use moderate admin commands (setjob, kick, etc.)
    ModeratorGroups = { 'mod', 'admin', 'senioradmin', 'cl', 'leadership', 'god' },
    
    -- Groups that can use basic commands (tp, heal, etc.)
    StaffGroups = { 'mod', 'admin', 'senioradmin', 'cl', 'leadership', 'god' },
    
    -- All valid ESX groups
    ValidGroups = { 'user', 'mod', 'admin', 'senioradmin', 'cl', 'leadership', 'god' },
    
    -- Valid jobs (add more as needed)
    ValidJobs = { 'unemployed', 'police', 'ambulance', 'mechanic', 'taxi', 'cardealer', 'banker' }
}

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Check if value is in a list
local function isInList(val, list)
    for _, v in ipairs(list) do
        if v == val then return true end
    end
    return false
end

-- Send chat message to player
local function sendMsg(target, msg)
    TriggerClientEvent('chat:addMessage', target, {
        templateId = 'system',
        multiline = true,
        args = {msg}
    })
end

-- Get player ID
local function getPlayerId(source)
    return source
end

-- Send success message
local function sendSuccess(target, msg)
    TriggerClientEvent('chat:addMessage', target, { 
        color = {0, 255, 0},
        args = {'SUCCESS', msg} 
    })
end

-- Send error message
local function sendError(target, msg)
    TriggerClientEvent('chat:addMessage', target, { 
        color = {255, 0, 0},
        args = {'ERROR', msg} 
    })
end

-- Check if player has permission for specific command type
local function hasPermission(xPlayer, permissionType)
    if not xPlayer then return false end
    local group = xPlayer.getGroup() or 'user'
    
    if permissionType == 'admin' then
        return isInList(group, Config.AdminGroups)
    elseif permissionType == 'moderator' then
        return isInList(group, Config.ModeratorGroups)
    elseif permissionType == 'staff' then
        return isInList(group, Config.StaffGroups)
    end
    
    return false
end

-- Get player by various methods (ID, name, etc.)
local function getTargetPlayer(identifier)
    local targetId = tonumber(identifier)
    
    -- Try by server ID first
    if targetId then
        return ESX.GetPlayerFromId(targetId)
    end
    
    -- Try by partial name
    local players = ESX.GetExtendedPlayers()
    for _, xPlayer in pairs(players) do
        if string.find(string.lower(xPlayer.getName()), string.lower(identifier)) then
            return xPlayer
        end
    end
    
    return nil
end

-- =====================================================
-- ADMIN COMMANDS
-- =====================================================

-- /setjob <player> <job> [grade]
RegisterCommand('setjob', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'moderator') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args < 2 then
        sendError(source, 'Usage: /setjob <player> <job> [grade]')
        sendMsg(source, 'Valid jobs: ' .. table.concat(Config.ValidJobs, ', '))
        return
    end

    local targetIdentifier = args[1]
    local jobName = args[2]:lower()
    local grade = tonumber(args[3]) or 0

    -- Check if job exists
    if not ESX.DoesJobExist(jobName, grade) then
        sendError(source, 'Invalid job or grade.')
        sendMsg(source, 'Valid jobs: ' .. table.concat(Config.ValidJobs, ', '))
        return
    end

    local xTarget = getTargetPlayer(targetIdentifier)
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    xTarget.setJob(jobName, grade)
    sendSuccess(source, ('Set %s\'s job to %s (Grade: %d)'):format(xTarget.getName(), jobName, grade))
    sendMsg(xTarget.source, ('Your job has been changed to: %s (Grade: %d)'):format(jobName, grade))

    print(('[SETJOB] %s set %s\'s job to %s (grade %d)'):format(xAdmin.getName(), xTarget.getName(), jobName, grade))
end, false)

-- /setgroup <player> <group>
RegisterCommand('setgroup', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'admin') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args < 2 then
        sendError(source, 'Usage: /setgroup <player> <group>')
        sendMsg(source, 'Valid groups: ' .. table.concat(Config.ValidGroups, ', '))
        return
    end

    local targetIdentifier = args[1]
    local newGroup = args[2]:lower()

    if not isInList(newGroup, Config.ValidGroups) then
        sendError(source, 'Invalid group.')
        sendMsg(source, 'Valid groups: ' .. table.concat(Config.ValidGroups, ', '))
        return
    end

    local xTarget = getTargetPlayer(targetIdentifier)
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    xTarget.setGroup(newGroup)
    sendSuccess(source, ('Set %s\'s group to %s'):format(xTarget.getName(), newGroup))
    sendMsg(xTarget.source, ('Your group has been changed to: %s'):format(newGroup))

    print(('[SETGROUP] %s set %s\'s group to %s'):format(xAdmin.getName(), xTarget.getName(), newGroup))
end, false)

-- /kick <player> [reason]
RegisterCommand('kick', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'moderator') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args < 1 then
        sendError(source, 'Usage: /kick <player> [reason]')
        return
    end

    local targetIdentifier = args[1]
    local reason = table.concat(args, ' ', 2) or 'No reason provided'

    local xTarget = getTargetPlayer(targetIdentifier)
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    DropPlayer(xTarget.source, ('Kicked by %s: %s'):format(xAdmin.getName(), reason))
    sendSuccess(source, ('Kicked %s for: %s'):format(xTarget.getName(), reason))

    print(('[KICK] %s kicked %s for: %s'):format(xAdmin.getName(), xTarget.getName(), reason))
end, false)

-- /heal <player>
RegisterCommand('heal', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    local targetIdentifier = args[1] or source
    local xTarget = getTargetPlayer(targetIdentifier)
    
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    TriggerClientEvent('sg_commands:heal', xTarget.source)
    sendSuccess(source, ('Healed %s'):format(xTarget.getName()))
    sendMsg(xTarget.source, 'You have been healed by an admin.')

    print(('[HEAL] %s healed %s'):format(xAdmin.getName(), xTarget.getName()))
end, false)

-- /tp <player> or /tp <x> <y> <z>
RegisterCommand('tp', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args == 1 then
        -- Teleport to player
        local xTarget = getTargetPlayer(args[1])
        if not xTarget then
            sendError(source, 'Player not found or not online.')
            return
        end
        
        TriggerClientEvent('sg_commands:teleportToPlayer', source, xTarget.source)
        sendSuccess(source, ('Teleported to %s'):format(xTarget.getName()))
        
    elseif #args == 3 then
        -- Teleport to coordinates
        local x, y, z = tonumber(args[1]), tonumber(args[2]), tonumber(args[3])
        if not x or not y or not z then
            sendError(source, 'Invalid coordinates.')
            return
        end
        
        TriggerClientEvent('sg_commands:teleportToCoords', source, x, y, z)
        sendSuccess(source, ('Teleported to %s, %s, %s'):format(x, y, z))
    else
        sendError(source, 'Usage: /tp <player> or /tp <x> <y> <z>')
    end
end, false)

-- /bring <player>
RegisterCommand('bring', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args < 1 then
        sendError(source, 'Usage: /bring <player>')
        return
    end

    local xTarget = getTargetPlayer(args[1])
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    TriggerClientEvent('sg_commands:bringPlayer', xTarget.source, source)
    sendSuccess(source, ('Brought %s to you'):format(xTarget.getName()))
    sendMsg(xTarget.source, ('You have been teleported to %s'):format(xAdmin.getName()))
end, false)

-- /goto <player>
RegisterCommand('goto', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    if #args < 1 then
        sendError(source, 'Usage: /goto <player>')
        return
    end

    local xTarget = getTargetPlayer(args[1])
    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    TriggerClientEvent('sg_commands:teleportToPlayer', source, xTarget.source)
    sendSuccess(source, ('Teleported to %s'):format(xTarget.getName()))
end, false)

-- /revive <player>
RegisterCommand('revive', function(source, args)
    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then return end

    if not hasPermission(xAdmin, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    local targetIdentifier = args[1] or source
    local xTarget = getTargetPlayer(targetIdentifier)

    if not xTarget then
        sendError(source, 'Player not found or not online.')
        return
    end

    TriggerClientEvent('sg_commands:revive', xTarget.source)
    sendSuccess(source, ('Revived %s'):format(xTarget.getName()))
    sendMsg(xTarget.source, 'You have been revived by an admin.')

    print(('[REVIVE] %s revived %s'):format(xAdmin.getName(), xTarget.getName()))
end, false)

-- =====================================================
-- INFORMATION COMMANDS
-- =====================================================

-- /group - show your current group
RegisterCommand('group', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local grp = xPlayer.getGroup() or 'user'
    sendMsg(source, ('Your group: %s'):format(grp))
end, false)

-- /groups - show all valid groups (admin only)
RegisterCommand('groups', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasPermission(xPlayer, 'moderator') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    sendMsg(source, 'Valid groups: ' .. table.concat(Config.ValidGroups, ', '))
end, false)

-- /job - show your current job
RegisterCommand('job', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local job = xPlayer.getJob()
    sendMsg(source, ('Your job: %s (Grade: %d)'):format(job.name, job.grade))
end, false)

-- /jobs - show all valid jobs (admin only)
RegisterCommand('jobs', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasPermission(xPlayer, 'moderator') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    sendMsg(source, 'Valid jobs: ' .. table.concat(Config.ValidJobs, ', '))
end, false)

-- /players - show online players
RegisterCommand('players', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    if not hasPermission(xPlayer, 'staff') then
        sendError(source, 'You do not have permission to use this command.')
        return
    end

    local players = ESX.GetExtendedPlayers()
    local playerList = {}

    for _, player in pairs(players) do
        table.insert(playerList, ('[%d] %s'):format(player.source, player.getName()))
    end

    sendMsg(source, ('Online Players (%d):'):format(#playerList))
    for _, playerInfo in ipairs(playerList) do
        sendMsg(source, playerInfo)
    end
end, false)

-- =====================================================
-- SERVER ANNOUNCEMENT COMMANDS
-- =====================================================

-- /announce - Server announcement (admin+ only)
RegisterCommand('announce', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- Permission check - admin and above
    if not xPlayer or not isInList(xPlayer.getGroup(), Config.AdminGroups) then
        sendMsg(source, '^1[ERROR]^0 You do not have permission to use this command.')
        return
    end

    if #args < 1 then
        sendMsg(source, '^3[USAGE]^0 /announce <message>')
        return
    end

    local message = table.concat(args, ' ')
    local staffName = xPlayer.getName()
    local staffGroup = xPlayer.getGroup()

    -- Format the announcement with Sapphire Gaming branding
    local announcement = string.format('[SAPPHIRE GAMING] %s', message)

    -- Send to all players using the new announcement template
    TriggerClientEvent('chat:addMessage', -1, {
        templateId = 'announcement',
        multiline = true,
        args = {announcement}
    })

    -- Also send as a notification for better visibility
    local xPlayers = ESX.GetPlayers()
    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer then
            TriggerClientEvent('esx:showNotification', targetPlayer.source, announcement, 'info', 8000)
        end
    end

    -- Log the announcement
    print(string.format('^3[SAPPHIRE GAMING ANNOUNCEMENT]^0 %s (%s) announced: %s', staffName, staffGroup, message))

    -- Send confirmation to the staff member
    sendMsg(source, string.format('^2[SUCCESS]^0 Server announcement sent: %s', message))

    -- Log to staff logs if available
    if GetResourceState('sg_staff') == 'started' then
        TriggerEvent('sg_staff:logAction', {
            staff_player_id = source,
            staff_name = staffName,
            staff_identifier = xPlayer.getIdentifier(),
            action_type = 'announcement',
            details = message,
            reason = 'Server announcement'
        })
    end
end, false)

-- /announce with custom color (leadership+ only)
RegisterCommand('cannounce', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- Permission check - leadership and above only
    if not xPlayer or not isInList(xPlayer.getGroup(), {'leadership', 'god'}) then
        sendMsg(source, '^1[ERROR]^0 You do not have permission to use this command.')
        return
    end

    if #args < 2 then
        sendMsg(source, '^3[USAGE]^0 /cannounce <color> <message>')
        sendMsg(source, '^3[COLORS]^0 red, green, blue, yellow, purple, orange, white')
        return
    end

    local color = args[1]:lower()
    table.remove(args, 1)
    local message = table.concat(args, ' ')
    local staffName = xPlayer.getName()

    -- Color mapping
    local colors = {
        red = {255, 0, 0},
        green = {0, 255, 0},
        blue = {0, 100, 255},
        yellow = {255, 255, 0},
        purple = {138, 43, 226},
        orange = {255, 165, 0},
        white = {255, 255, 255}
    }

    local selectedColor = colors[color] or colors.purple

    -- Format the announcement
    local announcement = string.format('^8[^5SAPPHIRE GAMING^8]^0 %s', message)

    -- Send to all players with custom color
    TriggerClientEvent('chatMessage', -1, '^5[ANNOUNCEMENT]', selectedColor, announcement)

    -- Log and confirm
    print(string.format('^3[SAPPHIRE GAMING ANNOUNCEMENT]^0 %s announced (%s): %s', staffName, color, message))
    sendMsg(source, string.format('^2[SUCCESS]^0 Custom announcement sent (%s): %s', color, message))
end, false)

-- =====================================================
-- DEBUG COMMANDS
-- =====================================================

-- /testsetgroup - debug setgroup permissions
RegisterCommand('testsetgroup', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print('[DEBUG] No xPlayer found for source: ' .. source)
        return
    end

    local grp = xPlayer.getGroup() or 'user'
    local hasPermission = IsPlayerAceAllowed(source, 'command.setgroup')

    sendMsg(source, ('DEBUG - Your group: %s | Has setgroup permission: %s'):format(grp, tostring(hasPermission)))
    print(('[DEBUG] Player %s (ID: %s) - Group: %s | Has setgroup permission: %s'):format(xPlayer.getName(), source, grp, tostring(hasPermission)))
end, false)

print('^2[Sapphire Gaming Commands]^0 Admin command system loaded successfully!')
