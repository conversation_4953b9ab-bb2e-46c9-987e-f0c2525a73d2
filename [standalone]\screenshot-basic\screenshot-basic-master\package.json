{"name": "screenshot-basic", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "MIT", "dependencies": {"@citizenfx/client": "^1.0.3404-1", "@citizenfx/http-wrapper": "^0.2.2", "@citizenfx/server": "^1.0.3404-1", "@citizenfx/three": "^0.100.0", "@types/koa": "^2.11.6", "@types/koa-router": "^7.4.1", "@types/mv": "^2.1.0", "@types/uuid": "^8.3.0", "html-webpack-inline-source-plugin": "^0.0.10", "html-webpack-plugin": "^3.2.0", "koa": "^2.6.2", "koa-body": "^4.0.6", "koa-router": "^7.4.0", "mv": "^2.1.1", "ts-loader": "^5.3.3", "typescript": "3.2.2", "uuid": "^3.3.2", "webpack": "4.28.4"}}