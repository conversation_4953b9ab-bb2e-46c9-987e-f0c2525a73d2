ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Staff groups that can use staff chat
local staffGroups = {
    ['god'] = true,
    ['leadership'] = true,
    ['cl'] = true,
    ['senioradmin'] = true,
    ['admin'] = true,
    ['mod'] = true
}

-- Staff groups that can use staff announcements (Senior Admin+)
local announceGroups = {
    ['god'] = true,
    ['leadership'] = true,
    ['cl'] = true,
    ['senioradmin'] = true
}

-- Register the /sc command
RegisterCommand('sc', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then
        return
    end
    
    local playerGroup = xPlayer.getGroup()
    
    -- Check if player is staff
    if not staffGroups[playerGroup] then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "You don't have permission to use staff chat."}
        })
        return
    end
    
    -- Check if message was provided
    if #args == 0 then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            multiline = true,
            args = {"Usage", "/sc <message>"}
        })
        return
    end
    
    -- Get the message
    local message = table.concat(args, ' ')
    local playerName = xPlayer.getName()
    
    -- Get group color
    local groupColors = {
        ['god'] = {255, 215, 0}, -- Gold
        ['leadership'] = {255, 0, 255}, -- Magenta
        ['cl'] = {0, 255, 255}, -- Cyan
        ['senioradmin'] = {255, 165, 0}, -- Orange
        ['admin'] = {255, 0, 0}, -- Red
        ['mod'] = {0, 255, 0} -- Green
    }
    
    local color = groupColors[playerGroup] or {255, 255, 255}
    
    -- Send message to all staff members
    local xPlayers = ESX.GetPlayers()
    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer and staffGroups[targetPlayer.getGroup()] then
            TriggerClientEvent('chat:addMessage', targetPlayer.source, {
                templateId = 'staff',
                multiline = true,
                args = {string.format("%s (%s)", playerName, playerGroup:upper()), message}
            })
        end
    end
    
    -- Log the staff chat message
    print(string.format('[STAFF CHAT] %s (%s): %s', playerName, playerGroup:upper(), message))
end, false)

-- Register the /sannounce command (Senior Admin+ only)
RegisterCommand('sannounce', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        return
    end

    local playerGroup = xPlayer.getGroup()

    -- Check if player has announcement permissions (Senior Admin+)
    if not announceGroups[playerGroup] then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "You don't have permission to use staff announcements. (Requires Senior Admin+)"}
        })
        return
    end

    -- Check if message was provided
    if #args == 0 then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            multiline = true,
            args = {"Usage", "/sannounce <message>"}
        })
        return
    end

    -- Get the message
    local message = table.concat(args, ' ')
    local playerName = xPlayer.getName()

    -- Send announcement to all staff members
    local xPlayers = ESX.GetPlayers()
    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer and staffGroups[targetPlayer.getGroup()] then
            -- Send as a prominent announcement using new template
            TriggerClientEvent('chat:addMessage', targetPlayer.source, {
                templateId = 'announcement',
                multiline = true,
                args = {string.format("📢 STAFF ANNOUNCEMENT from %s (%s): %s", playerName, playerGroup:upper(), message)}
            })
        end
    end

    -- Log the staff announcement
    print(string.format('[STAFF ANNOUNCEMENT] %s (%s): %s', playerName, playerGroup:upper(), message))
end, false)

print('^2[Sapphire Gaming Staff Chat]^0 Loaded successfully!')
print('^2[Sapphire Gaming Staff Chat]^0 Commands: /sc (all staff), /sannounce (Senior Admin+)')
