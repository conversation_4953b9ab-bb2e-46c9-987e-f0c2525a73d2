-- Sapphire Gaming Staff Panel Database Schema
-- This file creates all necessary tables for the integrated staff panel

-- Staff Actions Log Table
CREATE TABLE IF NOT EXISTS `sg_staff_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `staff_player_id` int(11) NOT NULL,
    `staff_name` varchar(100) NOT NULL,
    `staff_identifier` varchar(60) NOT NULL,
    `action_type` varchar(50) NOT NULL,
    `target_player_id` int(11) DEFAULT NULL,
    `target_name` varchar(100) DEFAULT NULL,
    `target_identifier` varchar(60) DEFAULT NULL,
    `details` longtext DEFAULT NULL,
    `reason` text DEFAULT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_staff_player_id` (`staff_player_id`),
    INDEX `idx_target_player_id` (`target_player_id`),
    INDEX `idx_action_type` (`action_type`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Bans Table (Enhanced from 919admin)
CREATE TABLE IF NOT EXISTS `sg_staff_bans` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `identifier` varchar(60) NOT NULL,
    `license` varchar(50) DEFAULT NULL,
    `discord` varchar(50) DEFAULT NULL,
    `ip` varchar(50) DEFAULT NULL,
    `reason` text NOT NULL,
    `banned_by_player_id` int(11) NOT NULL,
    `banned_by_name` varchar(100) NOT NULL,
    `banned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NULL DEFAULT NULL,
    `is_permanent` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `unbanned_by_player_id` int(11) DEFAULT NULL,
    `unbanned_by_name` varchar(100) DEFAULT NULL,
    `unbanned_at` timestamp NULL DEFAULT NULL,
    `unban_reason` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_player_id` (`player_id`),
    INDEX `idx_identifier` (`identifier`),
    INDEX `idx_license` (`license`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Warnings Table (Enhanced from 919admin)
CREATE TABLE IF NOT EXISTS `sg_staff_warnings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `identifier` varchar(60) NOT NULL,
    `reason` text NOT NULL,
    `warned_by_player_id` int(11) NOT NULL,
    `warned_by_name` varchar(100) NOT NULL,
    `warned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`),
    INDEX `idx_player_id` (`player_id`),
    INDEX `idx_identifier` (`identifier`),
    INDEX `idx_warned_at` (`warned_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reports Table (From 919admin)
CREATE TABLE IF NOT EXISTS `sg_staff_reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `reporter_player_id` int(11) NOT NULL,
    `reporter_name` varchar(100) NOT NULL,
    `reporter_identifier` varchar(60) NOT NULL,
    `subject` varchar(255) NOT NULL,
    `message` text NOT NULL,
    `status` enum('open','claimed','closed') DEFAULT 'open',
    `claimed_by_player_id` int(11) DEFAULT NULL,
    `claimed_by_name` varchar(100) DEFAULT NULL,
    `claimed_at` timestamp NULL DEFAULT NULL,
    `closed_by_player_id` int(11) DEFAULT NULL,
    `closed_by_name` varchar(100) DEFAULT NULL,
    `closed_at` timestamp NULL DEFAULT NULL,
    `close_reason` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_reporter_player_id` (`reporter_player_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin Chat Table
CREATE TABLE IF NOT EXISTS `sg_staff_chat` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `identifier` varchar(60) NOT NULL,
    `message` text NOT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_player_id` (`player_id`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Staff Notes Table (For player notes)
CREATE TABLE IF NOT EXISTS `sg_staff_notes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `target_player_id` int(11) NOT NULL,
    `target_name` varchar(100) NOT NULL,
    `target_identifier` varchar(60) NOT NULL,
    `note` text NOT NULL,
    `created_by_player_id` int(11) NOT NULL,
    `created_by_name` varchar(100) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `is_private` tinyint(1) DEFAULT 0,
    PRIMARY KEY (`id`),
    INDEX `idx_target_player_id` (`target_player_id`),
    INDEX `idx_created_by_player_id` (`created_by_player_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Staff Sessions Table (Track staff activity)
CREATE TABLE IF NOT EXISTS `sg_staff_sessions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `player_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `identifier` varchar(60) NOT NULL,
    `login_time` timestamp DEFAULT CURRENT_TIMESTAMP,
    `logout_time` timestamp NULL DEFAULT NULL,
    `duration_minutes` int(11) DEFAULT NULL,
    `actions_count` int(11) DEFAULT 0,
    PRIMARY KEY (`id`),
    INDEX `idx_player_id` (`player_id`),
    INDEX `idx_login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Vehicle Spawns Log Table
CREATE TABLE IF NOT EXISTS `sg_staff_vehicle_spawns` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `staff_player_id` int(11) NOT NULL,
    `staff_name` varchar(100) NOT NULL,
    `target_player_id` int(11) DEFAULT NULL,
    `target_name` varchar(100) DEFAULT NULL,
    `vehicle_model` varchar(50) NOT NULL,
    `spawn_coords` varchar(100) DEFAULT NULL,
    `spawned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_staff_player_id` (`staff_player_id`),
    INDEX `idx_target_player_id` (`target_player_id`),
    INDEX `idx_spawned_at` (`spawned_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Money Transactions Log Table
CREATE TABLE IF NOT EXISTS `sg_staff_money_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `staff_player_id` int(11) NOT NULL,
    `staff_name` varchar(100) NOT NULL,
    `target_player_id` int(11) NOT NULL,
    `target_name` varchar(100) NOT NULL,
    `action` enum('give','take') NOT NULL,
    `account_type` enum('money','bank','black_money') NOT NULL,
    `amount` int(11) NOT NULL,
    `reason` text DEFAULT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_staff_player_id` (`staff_player_id`),
    INDEX `idx_target_player_id` (`target_player_id`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Item Spawns Log Table
CREATE TABLE IF NOT EXISTS `sg_staff_item_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `staff_player_id` int(11) NOT NULL,
    `staff_name` varchar(100) NOT NULL,
    `target_player_id` int(11) NOT NULL,
    `target_name` varchar(100) NOT NULL,
    `action` enum('give','remove') NOT NULL,
    `item_name` varchar(50) NOT NULL,
    `quantity` int(11) NOT NULL,
    `reason` text DEFAULT NULL,
    `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_staff_player_id` (`staff_player_id`),
    INDEX `idx_target_player_id` (`target_player_id`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
