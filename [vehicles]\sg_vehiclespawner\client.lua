local cooldowns = {}
local spawnedBikes = {}

-- 3D Text function with proper styling
function Draw3DText(x, y, z)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextCentre(true)
        SetTextDropShadow(1, 0, 0, 0, 255)
        SetTextOutline()
        SetTextEntry("STRING")
        
        -- Only make the E blue, not the brackets
        local fullText = "[~b~E~w~] Free Bike"
        AddTextComponentString(fullText)
        DrawText(_x, _y)
    end
end

-- Check if player is on cooldown
function IsOnCooldown()
    local playerId = PlayerId()
    if cooldowns[playerId] then
        local timeLeft = cooldowns[playerId] - GetGameTimer()
        if timeLeft > 0 then
            local minutesLeft = math.ceil(timeLeft / 60000)
            return true, minutesLeft
        else
            cooldowns[playerId] = nil
            return false, 0
        end
    end
    return false, 0
end

-- Set cooldown for player
function SetCooldown()
    local playerId = PlayerId()
    cooldowns[playerId] = GetGameTimer() + (Config.CooldownTime * 1000)
end

-- Main thread for markers and interactions
CreateThread(function()
    while true do
        local player = PlayerPedId()
        local coords = GetEntityCoords(player)
        local sleep = true

        for i, location in ipairs(Config.Locations) do
            local dist = #(coords - location.markerCoords)

            if dist < Config.DrawDistance then
                sleep = false
                DrawMarker(
                    Config.MarkerType,
                    location.markerCoords.x, location.markerCoords.y, location.markerCoords.z - 0.97,
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                    Config.MarkerSize.x, Config.MarkerSize.y, Config.MarkerSize.z,
                    Config.MarkerColor.r, Config.MarkerColor.g, Config.MarkerColor.b, Config.MarkerColor.a,
                    false, true, 2, false, nil, nil, false
                )

                if dist < Config.InteractDistance then
                    Draw3DText(location.markerCoords.x, location.markerCoords.y, location.markerCoords.z + 0.3)
                    
                    if IsControlJustReleased(0, 38) then -- E key
                        local onCooldown, minutesLeft = IsOnCooldown()
                        
                        if onCooldown then
                            exports.ox_lib:notify({
                                title = 'Bike Rental',
                                description = 'You must wait ' .. minutesLeft .. ' more minutes before getting another bike',
                                type = 'error'
                            })
                        else
                            -- Clean up old bike if it exists but is deleted
                            if spawnedBikes[i] ~= nil and not DoesEntityExist(spawnedBikes[i]) then
                                spawnedBikes[i] = nil
                            end

                            RequestModel(location.bikeModel)
                            while not HasModelLoaded(location.bikeModel) do
                                Wait(10)
                            end

                            spawnedBikes[i] = CreateVehicle(
                                location.bikeModel,
                                location.spawnCoords.x, location.spawnCoords.y, location.spawnCoords.z, location.spawnCoords.w,
                                true, false
                            )
                            SetVehicleNumberPlateText(spawnedBikes[i], "RENTBIKE")
                            TaskWarpPedIntoVehicle(player, spawnedBikes[i], -1)
                            SetModelAsNoLongerNeeded(location.bikeModel)

                            SetCooldown()
                            
                            exports.ox_lib:notify({
                                title = 'Bike Rental',
                                description = 'Bike provided! You can get another in 5 minutes',
                                type = 'success'
                            })
                        end
                    end
                end
            end
        end

        if sleep then
            Wait(500)
        else
            Wait(0)
        end
    end
end)