Config = {}

-- Bike rental locations
Config.Locations = {
    {
        -- City Hall
        markerCoords = vector3(-520.7529, -262.5289, 35.4998), 
        spawnCoords = vector4(-516.1569, -264.1807, 35.3946, 289.8725), 
        bikeModel = "scorcher"
    },
    -- Add more locations here if needed
    -- {
    --     markerCoords = vector3(x, y, z),
    --     spawnCoords = vector4(x, y, z, heading),
    --     bikeModel = "bmx"
    -- }
}

-- Marker settings
Config.MarkerType = 27
Config.MarkerSize = {x = 1.3, y = 1.3, z = 1.2}
Config.MarkerColor = {r = 0, g = 191, b = 255, a = 150}
Config.DrawDistance = 40.0
Config.InteractDistance = 2.0

-- Cooldown time in seconds (5 minutes)
Config.CooldownTime = 300