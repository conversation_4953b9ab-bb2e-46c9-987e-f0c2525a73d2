// DO NOT EDIT THIS FILE
// Copy it to `config.js` and edit it
window.CONFIG = {
  defaultTemplateId: 'default', //This is the default template for 2 args1
  defaultAltTemplateId: 'defaultAlt', //This one for 1 arg
  templates: { //You can add static templates here
    'default': '<b>{0}</b>: {1}',
    'defaultAlt': '{0}',
    'print': '<pre>{0}</pre>',
    'example:important': '<h1>^2{0}</h1>',
    // Staff chat - all red and bold
    'staff': '<span style="color: #ff0000; font-weight: bold;">[STAFF] {0}: {1}</span>',
    'admin': '<span style="color: #ff0000; font-weight: bold;">[ADMIN] {0}: {1}</span>',
    // Announcements - red system message with white body, bold
    'announcement': '<span style="color: #ff0000; font-weight: bold;">[ANNOUNCEMENT]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    'system': '<span style="color: #ff0000; font-weight: bold;">[SYSTEM]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    // Enhanced roleplay commands - bold for cleaner look
    'ooc': '<span style="color: #5dade2; font-weight: bold;">[OOC]</span> <b>{0}</b>: <span style="font-weight: bold;">{1}</span>',
    'me': '<span style="color: #7bed9f; font-style: italic; font-weight: bold;">* {0} {1}</span>',
    'do': '<span style="color: #ff9ff3; font-style: italic; font-weight: bold;">* {1} (( {0} ))</span>'
  },
  fadeTimeout: 7000,
  suggestionLimit: 5,
  style: {
    background: 'rgba(52, 73, 94, 0.7)',
    width: '38%',
    height: '22%',
  }
};
