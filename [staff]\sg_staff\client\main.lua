ESX = nil

-- Wait for ESX to be ready
CreateThread(function()
    while not ESX do
        pcall(function()
            ESX = exports["es_extended"]:getSharedObject()
        end)
        if not ESX then
            Wait(100)
        end
    end
end)

local isStaffPanelOpen = false
local namesEnabled = false
local playersWithNames = {}
local staffMembers = {}
local currentPlayerData = {}

-- 919admin features
local isNoClipEnabled = false
local isSpectating = false
local spectateTarget = nil
local noClipSpeed = 1.0
local noClipCam = nil
local originalCoords = nil

-- Open staff panel
RegisterNetEvent('sg_staff:openPanel')
AddEventHandler('sg_staff:openPanel', function()
    print('^3[Sapphire Gaming Staff Panel Client]^0 Received openPanel event')

    if isStaffPanelOpen then
        print('^1[Sapphire Gaming Staff Panel Client]^0 Panel already open, ignoring')
        return
    end

    print('^2[Sapphire Gaming Staff Panel Client]^0 Opening staff panel...')
    isStaffPanelOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        type = 'openPanel'
    })

    print('^2[Sapphire Gaming Staff Panel Client]^0 NUI message sent, requesting data...')

    -- Request player data
    TriggerServerEvent('sg_staff:getPlayerData')
    TriggerServerEvent('sg_staff:getOnlinePlayers')
end)

-- Close staff panel
local function closeStaffPanel()
    if not isStaffPanelOpen then
        return
    end
    
    isStaffPanelOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        type = 'closePanel'
    })
end

-- Receive player data from server
RegisterNetEvent('sg_staff:receivePlayerData')
AddEventHandler('sg_staff:receivePlayerData', function(playerData)
    SendNUIMessage({
        type = 'updatePlayerData',
        data = playerData
    })
end)

-- Receive online players from server
RegisterNetEvent('sg_staff:receiveOnlinePlayers')
AddEventHandler('sg_staff:receiveOnlinePlayers', function(players)
    SendNUIMessage({
        type = 'updateOnlinePlayers',
        data = players
    })
end)

-- Receive logs from server
RegisterNetEvent('sg_staff:receiveLogs')
AddEventHandler('sg_staff:receiveLogs', function(logs)
    SendNUIMessage({
        type = 'updateLogs',
        data = logs
    })
end)

-- Receive server stats
RegisterNetEvent('sg_staff:receiveServerStats')
AddEventHandler('sg_staff:receiveServerStats', function(stats)
    SendNUIMessage({
        type = 'updateServerStats',
        data = stats
    })
end)

-- Receive inventory items
RegisterNetEvent('sg_staff:receiveInventoryItems')
AddEventHandler('sg_staff:receiveInventoryItems', function(items)
    SendNUIMessage({
        type = 'receiveInventoryItems',
        data = items
    })
end)

-- NUI Callbacks
RegisterNUICallback('closePanel', function(data, cb)
    closeStaffPanel()
    cb('ok')
end)

RegisterNUICallback('refreshData', function(data, cb)
    TriggerServerEvent('sg_staff:getPlayerData')
    TriggerServerEvent('sg_staff:getOnlinePlayers')
    TriggerServerEvent('sg_staff:getStaffMembers')
    TriggerServerEvent('sg_staff:getPlayersWithNames')
    TriggerServerEvent('sg_staff:getLogs')
    cb('ok')
end)

RegisterNUICallback('toggleNames', function(data, cb)
    toggleNames()
    cb('ok')
end)

-- Staff action callbacks
RegisterNUICallback('kickPlayer', function(data, cb)
    TriggerServerEvent('sg_staff:kickPlayer', data.playerId, data.reason or 'No reason provided')
    cb('ok')
end)

RegisterNUICallback('banPlayer', function(data, cb)
    TriggerServerEvent('sg_staff:banPlayer', data.playerId, data.reason or 'No reason provided', data.duration or 86400)
    cb('ok')
end)

RegisterNUICallback('teleportToPlayer', function(data, cb)
    TriggerServerEvent('sg_staff:teleportToPlayer', data.playerId)
    cb('ok')
end)

RegisterNUICallback('bringPlayer', function(data, cb)
    TriggerServerEvent('sg_staff:bringPlayer', data.playerId)
    cb('ok')
end)

-- Admin-only callbacks
RegisterNUICallback('killAllPlayers', function(data, cb)
    print('[SAPPHIRE GAMING STAFF PANEL] Kill All button clicked')
    TriggerServerEvent('sg_staff:killAllPlayers')
    cb('ok')
end)

RegisterNUICallback('revivePlayersRadius', function(data, cb)
    local radius = data.radius or 50.0
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Revive Radius button clicked (%.1fm)', radius))
    TriggerServerEvent('sg_staff:revivePlayersRadius', radius)
    cb('ok')
end)

RegisterNUICallback('deleteVehiclesRadius', function(data, cb)
    local radius = data.radius or 50.0
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Delete Vehicles Radius button clicked (%.1fm)', radius))
    TriggerServerEvent('sg_staff:deleteVehiclesRadius', radius)
    cb('ok')
end)

RegisterNUICallback('setServerTime', function(data, cb)
    TriggerServerEvent('sg_staff:setServerTime', data.hour, data.minute)
    cb('ok')
end)

RegisterNUICallback('setServerWeather', function(data, cb)
    TriggerServerEvent('sg_staff:setServerWeather', data.weather)
    cb('ok')
end)

-- TX Bans integration
RegisterNUICallback('txBansKick', function(data, cb)
    TriggerServerEvent('sg_staff:txBansKick', data.playerId, data.reason)
    cb('ok')
end)

RegisterNUICallback('txBansBan', function(data, cb)
    TriggerServerEvent('sg_staff:txBansBan', data.playerId, data.reason, data.duration)
    cb('ok')
end)

-- OX Inventory integration
RegisterNUICallback('clearInventory', function(data, cb)
    TriggerServerEvent('sg_staff:clearInventory', data.playerId)
    cb('ok')
end)

RegisterNUICallback('getInventoryItems', function(data, cb)
    TriggerServerEvent('sg_staff:getInventoryItems')
    cb('ok')
end)

RegisterNUICallback('giveItem', function(data, cb)
    TriggerServerEvent('sg_staff:giveItem', data.targetId, data.itemName, data.quantity)
    cb('ok')
end)

-- Names System Functions
function toggleNames()
    namesEnabled = not namesEnabled
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Names toggled: %s', tostring(namesEnabled)))
    TriggerServerEvent('sg_staff:toggleNames', namesEnabled)

    if namesEnabled then
        ESX.ShowNotification('~g~Names enabled')
    else
        ESX.ShowNotification('~r~Names disabled')
    end

    -- Update UI if panel is open
    if isStaffPanelOpen then
        SendNUIMessage({
            type = 'updateNamesStatus',
            enabled = namesEnabled
        })
    end
end

-- Register /names command
RegisterCommand('names', function()
    local playerData = ESX.GetPlayerData()
    if playerData and playerData.group then
        -- Check if player has staff permission
        local allowedGroups = {
            ['owner'] = true,
            ['admin'] = true,
            ['mod'] = true,
            ['cl'] = true
        }

        if allowedGroups[playerData.group] then
            toggleNames()
        else
            ESX.ShowNotification('~r~You do not have permission to use this command.')
        end
    else
        ESX.ShowNotification('~r~Unable to get player data.')
    end
end, false)

-- Receive staff members data
RegisterNetEvent('sg_staff:receiveStaffMembers')
AddEventHandler('sg_staff:receiveStaffMembers', function(staff)
    staffMembers = staff
end)

-- Receive players with names enabled
RegisterNetEvent('sg_staff:receivePlayersWithNames')
AddEventHandler('sg_staff:receivePlayersWithNames', function(players)
    playersWithNames = players
end)

-- Names display thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5) -- Small wait to improve performance

        if namesEnabled then
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local myPlayerId = PlayerId()
            local myServerId = GetPlayerServerId(myPlayerId)

            -- First, display own name
            local myName = GetPlayerName(myPlayerId)
            local myHealth = GetEntityHealth(playerPed)
            local myMaxHealth = GetEntityMaxHealth(playerPed)
            local myHealthPercent = (myHealth / myMaxHealth) * 100

            -- Check if I'm staff
            local myStaffInfo = nil
            if staffMembers then
                for _, staff in pairs(staffMembers) do
                    if staff.source == myServerId then
                        myStaffInfo = staff
                        break
                    end
                end
            end

            -- Display my own name using ESX 3D text with larger scale
            local myCoords = GetEntityCoords(playerPed)
            local myDisplayText = string.format("~r~[%d] %s", myServerId, myName) -- Red for self
            if myStaffInfo then
                local rankText = ""
                if myStaffInfo.level == 1 then
                    rankText = "~r~[OWNER]"  -- Owner in red
                elseif myStaffInfo.level == 2 then
                    rankText = "~o~[CL]"     -- CL in orange
                elseif myStaffInfo.level == 3 then
                    rankText = "~b~[ADMIN]"  -- Admin in blue
                elseif myStaffInfo.level == 4 then
                    rankText = "~g~[MOD]"    -- Mod in green
                end
                myDisplayText = string.format("%s ~r~[%d] %s", rankText, myServerId, myName)
            end

            -- Use ESX's built-in 3D text function with much larger size
            local myTextCoords = vector3(myCoords.x, myCoords.y, myCoords.z + 1.3)
            ESX.Game.Utils.DrawText3D(myTextCoords, myDisplayText, 1.2, 4)

            -- Draw my health bar with fancy styling
            local myHealthScreenX, myHealthScreenY = GetScreenCoordFromWorldCoord(myCoords.x, myCoords.y, myCoords.z + 1.1)
            if myHealthScreenX and myHealthScreenY and myHealthScreenX > 0.0 and myHealthScreenX < 1.0 and myHealthScreenY > 0.0 and myHealthScreenY < 1.0 then
                local myBarWidth = 0.15
                local myBarHeight = 0.015
                local myBarX = myHealthScreenX - (myBarWidth / 2)
                local myBarY = myHealthScreenY + 0.04

                -- Outer border (black)
                DrawRect(myBarX + (myBarWidth / 2), myBarY, myBarWidth + 0.006, myBarHeight + 0.006, 0, 0, 0, 255)

                -- Background bar (dark gray)
                DrawRect(myBarX + (myBarWidth / 2), myBarY, myBarWidth, myBarHeight, 40, 40, 40, 255)

                -- Health bar with gradient effect
                local myHealthWidth = (myBarWidth * myHealthPercent) / 100
                local healthColor = {0, 255, 0} -- Green
                if myHealthPercent < 30 then
                    healthColor = {255, 0, 0} -- Red for low health
                elseif myHealthPercent < 60 then
                    healthColor = {255, 165, 0} -- Orange for medium health
                end

                DrawRect(myBarX + (myHealthWidth / 2), myBarY, myHealthWidth, myBarHeight, healthColor[1], healthColor[2], healthColor[3], 255)

                -- Health percentage text using ESX 3D text
                local healthText = string.format("~w~%d%%", math.floor(myHealthPercent))
                local healthTextCoords = vector3(myCoords.x, myCoords.y, myCoords.z + 0.9)
                ESX.Game.Utils.DrawText3D(healthTextCoords, healthText, 0.6, 4)
            end

            -- Now display other players
            local activePlayers = GetActivePlayers()

            for _, player in ipairs(activePlayers) do
                local targetPed = GetPlayerPed(player)
                local targetCoords = GetEntityCoords(targetPed)
                local distance = #(playerCoords - targetCoords)

                if distance <= 50.0 and targetPed ~= playerPed then
                    local playerId = GetPlayerServerId(player)
                    local playerName = GetPlayerName(player)
                    local health = GetEntityHealth(targetPed)
                    local maxHealth = GetEntityMaxHealth(targetPed)
                    local healthPercent = (health / maxHealth) * 100

                    -- Check if player is staff
                    local isStaff = false
                    local staffInfo = nil
                    if staffMembers then
                        for _, staff in pairs(staffMembers) do
                            if staff.source == playerId then
                                isStaff = true
                                staffInfo = staff
                                break
                            end
                        end
                    end

                    -- Check if staff member has names enabled
                    local isStaffWithNames = false
                    if playersWithNames then
                        for _, playerWithNames in pairs(playersWithNames) do
                            if playerWithNames == playerId and isStaff then
                                isStaffWithNames = true
                                break
                            end
                        end
                    end

                    -- Draw name above player
                    local nameColor = {255, 255, 255} -- White for regular players
                    if isStaffWithNames then
                        nameColor = {255, 0, 0} -- Red for staff with names enabled
                    end

                    -- Display player name using ESX 3D text with larger scale
                    local displayText = string.format("~w~[%d] %s", playerId, playerName) -- White for regular players
                    if isStaff and staffInfo then
                        local rankText = ""
                        if staffInfo.level == 1 then
                            rankText = "~r~[OWNER]"  -- Owner in red
                        elseif staffInfo.level == 2 then
                            rankText = "~o~[CL]"     -- CL in orange
                        elseif staffInfo.level == 3 then
                            rankText = "~b~[ADMIN]"  -- Admin in blue
                        elseif staffInfo.level == 4 then
                            rankText = "~g~[MOD]"    -- Mod in green
                        end
                        displayText = string.format("%s ~w~[%d] %s", rankText, playerId, playerName)
                    end

                    -- Color the name red if staff has names enabled
                    if isStaffWithNames then
                        displayText = displayText:gsub("~w~", "~r~") -- Change white to red for staff with names
                    end

                    -- Use ESX's built-in 3D text function with much larger size
                    local textCoords = vector3(targetCoords.x, targetCoords.y, targetCoords.z + 1.3)
                    ESX.Game.Utils.DrawText3D(textCoords, displayText, 1.2, 4)

                    -- Draw fancy health bar below the text
                    local healthScreenX, healthScreenY = GetScreenCoordFromWorldCoord(targetCoords.x, targetCoords.y, targetCoords.z + 1.1)
                    if healthScreenX and healthScreenY and healthScreenX > 0.0 and healthScreenX < 1.0 and healthScreenY > 0.0 and healthScreenY < 1.0 then
                        local barWidth = 0.15
                        local barHeight = 0.015
                        local barX = healthScreenX - (barWidth / 2)
                        local barY = healthScreenY + 0.04

                        -- Outer border (black)
                        DrawRect(barX + (barWidth / 2), barY, barWidth + 0.006, barHeight + 0.006, 0, 0, 0, 255)

                        -- Background bar (dark gray)
                        DrawRect(barX + (barWidth / 2), barY, barWidth, barHeight, 40, 40, 40, 255)

                        -- Health bar with gradient effect
                        local healthWidth = (barWidth * healthPercent) / 100
                        local healthColor = {0, 255, 0} -- Green
                        if healthPercent < 30 then
                            healthColor = {255, 0, 0} -- Red for low health
                        elseif healthPercent < 60 then
                            healthColor = {255, 165, 0} -- Orange for medium health
                        end

                        DrawRect(barX + (healthWidth / 2), barY, healthWidth, barHeight, healthColor[1], healthColor[2], healthColor[3], 255)

                        -- Health percentage text using ESX 3D text
                        local healthText = string.format("~w~%d%%", math.floor(healthPercent))
                        local healthTextCoords = vector3(targetCoords.x, targetCoords.y, targetCoords.z + 0.9)
                        ESX.Game.Utils.DrawText3D(healthTextCoords, healthText, 0.6, 4)
                    end
                end
            end
        else
            Citizen.Wait(1000) -- Wait longer when names are disabled
        end
    end
end)

-- Sync time from server
RegisterNetEvent('sg_staff:syncTime')
AddEventHandler('sg_staff:syncTime', function(hour, minute)
    Citizen.CreateThread(function()
        NetworkOverrideClockTime(hour, minute, 0)
        -- Ensure the time is properly set
        Citizen.Wait(100)
        NetworkOverrideClockTime(hour, minute, 0)
    end)
end)

-- Sync weather from server
RegisterNetEvent('sg_staff:syncWeather')
AddEventHandler('sg_staff:syncWeather', function(weather)
    SetWeatherTypeNowPersist(weather)
    SetWeatherTypeNow(weather)
    SetWeatherTypePersist(weather)
end)

-- Force kill player
RegisterNetEvent('sg_staff:forceKill')
AddEventHandler('sg_staff:forceKill', function()
    local playerPed = PlayerPedId()
    print('[SAPPHIRE GAMING STAFF PANEL] Force kill triggered for player')

    Citizen.CreateThread(function()
        -- Simple but effective kill method
        SetEntityHealth(playerPed, 0)

        -- Wait a moment and check if it worked
        Citizen.Wait(100)

        if GetEntityHealth(playerPed) > 0 then
            -- If still alive, try more aggressive methods
            ApplyDamageToPed(playerPed, 10000, false)
            SetPedToRagdoll(playerPed, 5000, 5000, 0, 0, 0, 0)

            -- Wait and try again
            Citizen.Wait(100)
            SetEntityHealth(playerPed, 0)

            print(string.format('[SAPPHIRE GAMING STAFF PANEL] Kill attempt - Final health: %d', GetEntityHealth(playerPed)))
        else
            print('[SAPPHIRE GAMING STAFF PANEL] Force kill - successful')
        end
    end)
end)

-- Force heal player (for alive players)
RegisterNetEvent('sg_staff:forceHeal')
AddEventHandler('sg_staff:forceHeal', function()
    local playerPed = PlayerPedId()
    print('[SAPPHIRE GAMING STAFF PANEL] Force heal triggered')

    Citizen.CreateThread(function()
        local maxHealth = GetEntityMaxHealth(playerPed)
        local currentHealth = GetEntityHealth(playerPed)

        -- Set to full health
        SetEntityHealth(playerPed, maxHealth)

        -- Clear any damage effects
        ClearPedBloodDamage(playerPed)
        ResetPedVisibleDamage(playerPed)
        ClearPedLastWeaponDamage(playerPed)

        print(string.format('[SAPPHIRE GAMING STAFF PANEL] Healed player from %d to %d HP', currentHealth, maxHealth))
    end)
end)

-- Force revive player (for dead players)
RegisterNetEvent('sg_staff:forceRevive')
AddEventHandler('sg_staff:forceRevive', function()
    local playerPed = PlayerPedId()
    print('[SAPPHIRE GAMING STAFF PANEL] Force revive triggered')

    Citizen.CreateThread(function()
        local coords = GetEntityCoords(playerPed)
        local maxHealth = GetEntityMaxHealth(playerPed)

        -- Revive the player if dead
        if IsEntityDead(playerPed) then
            NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, GetEntityHeading(playerPed), true, false)
            Wait(100)
        end

        -- Set to full health
        SetEntityHealth(playerPed, maxHealth)

        -- Clear any damage effects
        ClearPedBloodDamage(playerPed)
        ResetPedVisibleDamage(playerPed)
        ClearPedLastWeaponDamage(playerPed)

        print('[SAPPHIRE GAMING STAFF PANEL] Player revived and healed to full health')
    end)
end)

-- Force revive player (for dead players)
RegisterNetEvent('sg_staff:forceRevive')
AddEventHandler('sg_staff:forceRevive', function()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    print('[SAPPHIRE GAMING STAFF PANEL] Force revive triggered')

    Citizen.CreateThread(function()
        local wasDead = IsPedDeadOrDying(playerPed, true)
        print('[SAPPHIRE GAMING STAFF PANEL] Player was dead:', wasDead)

        -- Set to full health first
        SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))

        -- If still dead, use network resurrection
        if IsPedDeadOrDying(playerPed, true) then
            NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, GetEntityHeading(playerPed), true, false)
            print('[SAPPHIRE GAMING STAFF PANEL] Used NetworkResurrectLocalPlayer')
        end

        -- Clear effects
        ClearPedBloodDamage(playerPed)
        ResetPedVisibleDamage(playerPed)
        SetPedCanRagdoll(playerPed, true)

        print('[SAPPHIRE GAMING STAFF PANEL] Force revive completed')
    end)
end)

-- Emergency close command
RegisterCommand('closestaffpanel', function()
    if isStaffPanelOpen then
        closeStaffPanel()
        ESX.ShowNotification('~g~Staff panel force closed')
    end
end, false)

-- NoClip command (919admin feature)
RegisterCommand('noclip', function()
    local playerData = ESX.GetPlayerData()
    if playerData and playerData.group then
        local allowedGroups = {
            ['god'] = true,
            ['owner'] = true,
            ['admin'] = true,
            ['mod'] = true,
            ['cl'] = true
        }

        if allowedGroups[playerData.group] then
            toggleNoClip()
        else
            ESX.ShowNotification('~r~You do not have permission to use noclip.')
        end
    end
end, false)

-- NoClip functionality
function toggleNoClip()
    isNoClipEnabled = not isNoClipEnabled
    local playerPed = PlayerPedId()

    if isNoClipEnabled then
        originalCoords = GetEntityCoords(playerPed)
        noClipCam = CreateCam('DEFAULT_SCRIPTED_CAMERA', true)
        SetCamCoord(noClipCam, originalCoords.x, originalCoords.y, originalCoords.z)
        SetCamRot(noClipCam, 0.0, 0.0, GetEntityHeading(playerPed))
        SetCamActive(noClipCam, true)
        RenderScriptCams(true, false, 0, true, true)

        SetEntityVisible(playerPed, false, false)
        SetEntityInvincible(playerPed, true)
        FreezeEntityPosition(playerPed, true)
        SetEntityCollision(playerPed, false, false)

        ESX.ShowNotification('~g~NoClip enabled')
    else
        if noClipCam then
            local camCoords = GetCamCoord(noClipCam)
            SetEntityCoords(playerPed, camCoords.x, camCoords.y, camCoords.z, false, false, false, false)

            DestroyCam(noClipCam, false)
            RenderScriptCams(false, false, 0, true, true)
            noClipCam = nil
        end

        SetEntityVisible(playerPed, true, false)
        SetEntityInvincible(playerPed, false)
        FreezeEntityPosition(playerPed, false)
        SetEntityCollision(playerPed, true, true)

        ESX.ShowNotification('~r~NoClip disabled')
    end
end

-- NoClip movement thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        if isNoClipEnabled and noClipCam then
            local camCoords = GetCamCoord(noClipCam)
            local camRot = GetCamRot(noClipCam, 2)

            -- Speed control
            local speed = noClipSpeed
            if IsControlPressed(0, 21) then -- Left Shift
                speed = speed * 2.5
            elseif IsControlPressed(0, 36) then -- Left Ctrl
                speed = speed * 0.5
            end

            -- Movement
            local newCoords = camCoords
            local forward = GetEntityForwardVector(PlayerPedId())
            local right = vector3(-forward.y, forward.x, 0.0)

            if IsControlPressed(0, 32) then -- W
                newCoords = newCoords + (forward * speed)
            end
            if IsControlPressed(0, 33) then -- S
                newCoords = newCoords - (forward * speed)
            end
            if IsControlPressed(0, 34) then -- A
                newCoords = newCoords - (right * speed)
            end
            if IsControlPressed(0, 35) then -- D
                newCoords = newCoords + (right * speed)
            end
            if IsControlPressed(0, 22) then -- Space
                newCoords = newCoords + vector3(0.0, 0.0, speed)
            end
            if IsControlPressed(0, 36) then -- Left Ctrl
                newCoords = newCoords - vector3(0.0, 0.0, speed)
            end

            -- Mouse look
            local mouseX = GetDisabledControlNormal(0, 1) * 5.0
            local mouseY = GetDisabledControlNormal(0, 2) * 5.0

            local newRot = vector3(
                math.max(-90.0, math.min(90.0, camRot.x - mouseY)),
                camRot.y,
                camRot.z - mouseX
            )

            SetCamCoord(noClipCam, newCoords.x, newCoords.y, newCoords.z)
            SetCamRot(noClipCam, newRot.x, newRot.y, newRot.z, 2)

            -- Disable controls
            DisableAllControlActions(0)
            EnableControlAction(0, 1, true) -- Mouse look
            EnableControlAction(0, 2, true) -- Mouse look
        else
            Citizen.Wait(500)
        end
    end
end)

-- Vehicle spawning (919admin feature)
RegisterNetEvent('sg_staff:spawnVehicleClient')
AddEventHandler('sg_staff:spawnVehicleClient', function(vehicleModel)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local heading = GetEntityHeading(playerPed)

    -- Calculate spawn position in front of player
    local forward = GetEntityForwardVector(playerPed)
    local spawnCoords = coords + (forward * Config.Vehicles.spawnDistance)

    ESX.Game.SpawnVehicle(vehicleModel, spawnCoords, heading, function(vehicle)
        if vehicle then
            SetPedIntoVehicle(playerPed, vehicle, -1)
            ESX.ShowNotification(string.format('~g~Vehicle %s spawned', vehicleModel))
        else
            ESX.ShowNotification('~r~Failed to spawn vehicle')
        end
    end)
end)

-- Spectate functionality (919admin feature)
RegisterNetEvent('sg_staff:startSpectate')
AddEventHandler('sg_staff:startSpectate', function(targetId)
    if isSpectating then
        return
    end

    isSpectating = true
    spectateTarget = targetId

    local playerPed = PlayerPedId()
    originalCoords = GetEntityCoords(playerPed)

    SetEntityVisible(playerPed, false, false)
    SetEntityInvincible(playerPed, true)
    FreezeEntityPosition(playerPed, true)
    SetEntityCollision(playerPed, false, false)

    NetworkSetInSpectatorMode(true, GetPlayerPed(GetPlayerFromServerId(targetId)))

    ESX.ShowNotification('~g~Spectating started. Press ~INPUT_CONTEXT~ to stop.')
end)

RegisterNetEvent('sg_staff:stopSpectate')
AddEventHandler('sg_staff:stopSpectate', function()
    if not isSpectating then
        return
    end

    isSpectating = false
    spectateTarget = nil

    NetworkSetInSpectatorMode(false, PlayerPedId())

    local playerPed = PlayerPedId()
    if originalCoords then
        SetEntityCoords(playerPed, originalCoords.x, originalCoords.y, originalCoords.z, false, false, false, false)
    end

    SetEntityVisible(playerPed, true, false)
    SetEntityInvincible(playerPed, false)
    FreezeEntityPosition(playerPed, false)
    SetEntityCollision(playerPed, true, true)

    ESX.ShowNotification('~r~Spectating stopped')
end)

-- Spectate controls thread
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        if isSpectating then
            if IsControlJustPressed(0, 38) then -- E key
                TriggerServerEvent('sg_staff:stopSpectate')
            end
        else
            Citizen.Wait(500)
        end
    end
end)

-- Screenshot functionality (919admin feature)
RegisterNetEvent('sg_staff:takeScreenshot')
AddEventHandler('sg_staff:takeScreenshot', function(staffName)
    if not Config.Screenshot.enabled then
        return
    end

    exports['screenshot-basic']:requestScreenshotUpload(Config.Screenshot.webhook, 'files[]', function(data)
        local response = json.decode(data)
        if response and response.attachments and response.attachments[1] then
            local imageUrl = response.attachments[1].url

            -- Send screenshot info to Discord
            PerformHttpRequest(Config.Screenshot.webhook, function(err, text, headers) end, 'POST', json.encode({
                username = 'Sapphire Gaming Screenshots',
                embeds = {{
                    title = 'Player Screenshot',
                    description = string.format('Screenshot taken by **%s**', staffName),
                    image = { url = imageUrl },
                    color = Config.Discord.colors.general,
                    timestamp = os.date('!%Y-%m-%dT%H:%M:%SZ'),
                    footer = {
                        text = 'Sapphire Gaming Staff Panel'
                    }
                }}
            }), { ['Content-Type'] = 'application/json' })
        end
    end)

    ESX.ShowNotification(string.format('~g~Screenshot taken by %s', staffName))
end)

-- Warning notification (919admin feature)
RegisterNetEvent('sg_staff:receiveWarning')
AddEventHandler('sg_staff:receiveWarning', function(reason, staffName)
    ESX.ShowNotification(string.format('~o~You have been warned by %s\nReason: %s', staffName, reason))

    -- Show warning on screen for 10 seconds
    Citizen.CreateThread(function()
        local startTime = GetGameTimer()
        while GetGameTimer() - startTime < 10000 do
            Citizen.Wait(0)
            SetTextFont(4)
            SetTextProportional(1)
            SetTextScale(0.8, 0.8)
            SetTextColour(255, 165, 0, 255)
            SetTextDropShadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString(string.format("WARNING\nStaff: %s\nReason: %s", staffName, reason))
            DrawText(0.5, 0.3)
        end
    end)
end)

-- Close panel with ESC key
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if isStaffPanelOpen then
            if IsControlJustPressed(0, 322) then -- ESC key
                closeStaffPanel()
            end
        else
            Citizen.Wait(500)
        end
    end
end)
