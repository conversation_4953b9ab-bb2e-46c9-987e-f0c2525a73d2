-- ═══════════════════════════════════════════════════════════════════════════════
-- VOID CLAIMING SYSTEM - GAME MODE INTEGRATION
-- This file ensures all game modes work properly with the claiming system
-- Includes integrated deagle_only functionality (no separate resource needed)
-- ═══════════════════════════════════════════════════════════════════════════════

-- ═══════════════════════════════════════════════════════════════════════════════
-- INTEGRATED DEAGLE ONLY SYSTEM
-- ═══════════════════════════════════════════════════════════════════════════════

local deagleOnlyEnabled = false
local DeagleOnly = false
local DeagleHash = GetHashKey("weapon_pistol50")
local lastNotifyTime = 0

-- ═══════════════════════════════════════════════════════════════════════════════
-- INTEGRATED X2 HEADDY SYSTEM
-- ═══════════════════════════════════════════════════════════════════════════════

local x2HeaddyEnabled = false
local X2Headdy = false

-- Function to reset weapon damage to normal (1-tap headshots)
local function resetWeaponDamage()
    -- Force reset weapon damage modifier multiple times to ensure it sticks
    SetWeaponDamageModifier(DeagleHash, 1.0) -- Reset to 100% normal damage
    Citizen.Wait(10)
    SetWeaponDamageModifier(DeagleHash, 1.0) -- Reset again to be sure
    Citizen.Wait(10)
    SetWeaponDamageModifier(DeagleHash, 1.0) -- Third time to ensure it's applied
    print('[WEAPON RESET] Pistol50 damage FORCE RESET to normal (1-tap headshots)')
end

-- Safety cleanup when resource stops
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        -- Ensure x2headdy is disabled and weapon damage is reset
        X2Headdy = false
        resetWeaponDamage()
        print('[X2HEADDY] Resource stopped - x2 Headdy mode disabled and weapon damage reset')
    end
end)

-- Comprehensive weapon list for removal during deagle only mode
local ConfigWeapons = {
    -- Melee weapons
    "weapon_knife", "weapon_bat", "weapon_machete", "weapon_poolcue", "weapon_wrench",
    "weapon_dagger", "weapon_hatchet", "weapon_knuckle", "weapon_nightstick",
    "weapon_hammer", "weapon_crowbar", "weapon_golfclub", "weapon_bottle",
    "weapon_switchblade", "weapon_battleaxe", "weapon_stone_hatchet",

    -- Pistols (except deagle)
    "weapon_pistol", "weapon_combatpistol", "weapon_appistol", "weapon_stungun",
    "weapon_vintagepistol", "weapon_flaregun", "weapon_marksmanpistol",
    "weapon_revolver", "weapon_snspistol", "weapon_heavypistol", "weapon_pistol_mk2",
    "weapon_snspistol_mk2", "weapon_revolver_mk2", "weapon_doubleaction",
    "weapon_ceramicpistol", "weapon_navyrevolver", "weapon_gadgetpistol",

    -- SMGs
    "weapon_smg", "weapon_microsmg", "weapon_assaultsmg", "weapon_machinepistol",
    "weapon_minismg", "weapon_combatpdw", "weapon_smg_mk2", "weapon_mp5",
    "weapon_mp7", "weapon_mp9a", "weapon_g18c",

    -- Assault Rifles
    "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle",
    "weapon_specialcarbine", "weapon_bullpuprifle", "weapon_compactrifle",
    "weapon_militaryrifle", "weapon_heavyrifle", "weapon_tacticalrifle",
    "weapon_assaultrifle_mk2", "weapon_carbinerifle_mk2", "weapon_specialcarbine_mk2",
    "weapon_bullpuprifle_mk2", "weapon_m16", "weapon_mk47", "weapon_m4a1",
    "weapon_scar", "weapon_famas", "weapon_l85a2",

    -- Shotguns
    "weapon_pumpshotgun", "weapon_sawnoffshotgun", "weapon_assaultshotgun",
    "weapon_bullpupshotgun", "weapon_musket", "weapon_heavyshotgun",
    "weapon_dbshotgun", "weapon_autoshotgun", "weapon_pumpshotgun_mk2",
    "weapon_combatshotgun",

    -- Sniper Rifles
    "weapon_sniperrifle", "weapon_heavysniper", "weapon_marksmanrifle",
    "weapon_remotesniper", "weapon_heavysniper_mk2", "weapon_marksmanrifle_mk2",

    -- Heavy Weapons
    "weapon_rpg", "weapon_grenadelauncher", "weapon_grenadelauncher_smoke",
    "weapon_minigun", "weapon_firework", "weapon_railgun", "weapon_hominglauncher",
    "weapon_compactlauncher", "weapon_rayminigun", "weapon_emplauncher",

    -- Machine Guns
    "weapon_mg", "weapon_combatmg", "weapon_gusenberg", "weapon_combatmg_mk2",
    "weapon_p90",

    -- Throwables
    "weapon_grenade", "weapon_bzgas", "weapon_molotov", "weapon_stickybomb",
    "weapon_proxmine", "weapon_snowball", "weapon_pipebomb", "weapon_ball",
    "weapon_smokegrenade", "weapon_flare",

    -- Other
    "weapon_fireextinguisher", "weapon_petrolcan", "weapon_parachute",
    "weapon_hazardcan", "weapon_fertilizercan"
}

-- Deagle only mode toggle event
RegisterNetEvent("deagle:setMode")
AddEventHandler("deagle:setMode", function(state)
    DeagleOnly = state
    local ped = PlayerPedId()
    RemoveAllPedWeapons(ped, true)
    if DeagleOnly then
        GiveWeaponToPed(ped, DeagleHash, 250, false, true)
        SetCurrentPedWeapon(ped, DeagleHash, true)
        -- No notification when enabled via claiming system
    else
        -- No notification when disabled via claiming system
    end
end)

-- x2 Headdy mode toggle event
RegisterNetEvent("x2headdy:setMode")
AddEventHandler("x2headdy:setMode", function(state)
    X2Headdy = state
    local ped = PlayerPedId()

    if X2Headdy then
        -- Enable x2 headdy mode (deagle only + custom damage)
        RemoveAllPedWeapons(ped, true)
        GiveWeaponToPed(ped, DeagleHash, 250, false, true)
        SetCurrentPedWeapon(ped, DeagleHash, true)

        print('[X2HEADDY] x2 Headdy mode ENABLED - HEADSHOT ONLY damage system active')
    else
        -- Reset weapon damage to normal when disabling x2headdy
        resetWeaponDamage()



        -- Also immediately set damage modifier to ensure it's applied
        Citizen.CreateThread(function()
            for i = 1, 10 do -- Reset 10 times over 1 second to ensure it sticks
                SetWeaponDamageModifier(DeagleHash, 1.0)
                Citizen.Wait(100)
            end
        end)
        print('[X2HEADDY] x2 Headdy mode DISABLED - Normal weapon damage restored')
    end
end)

-- x2 Headdy damage override system - ONLY active during x2headdy rounds
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        -- ONLY apply damage modifier during x2headdy mode
        -- All other modes (helicopter, deagleonly, any, etc.) use normal weapon damage (1-tap headshots)
        if X2Headdy then
            -- Reduce damage significantly to allow custom damage control
            SetWeaponDamageModifierThisFrame(DeagleHash, 0.1) -- Very low damage - we'll override it
        end
        -- When X2Headdy is false, no modifier is applied = normal 1-tap headshot behavior
    end
end)

-- Periodic weapon damage check to ensure it stays correct
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- Check every 1 second for more responsive fixing

        -- If x2headdy is not active, ensure weapon damage is normal
        if not X2Headdy then
            SetWeaponDamageModifier(DeagleHash, 1.0) -- Ensure normal damage every second
        end
    end
end)

-- Comprehensive damage blocking system for x2 headdy mode
-- This system ensures ONLY headshots can deal damage
local lastHealthCheck = {}
local damageProcessed = {}

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(50)

        -- ONLY process custom damage during x2headdy rounds
        if X2Headdy then
            local players = GetActivePlayers()

            for _, playerId in ipairs(players) do
                local playerPed = GetPlayerPed(playerId)
                local currentHealth = GetEntityHealth(playerPed)
                local lastHealth = lastHealthCheck[playerId] or currentHealth

                -- If player took damage and we haven't processed this damage event yet
                if currentHealth < lastHealth and currentHealth > 0 then
                    local damageKey = playerId .. "_" .. currentHealth .. "_" .. lastHealth

                    if not damageProcessed[damageKey] then
                        damageProcessed[damageKey] = true

                        -- Clear old damage keys to prevent memory buildup
                        Citizen.SetTimeout(5000, function()
                            damageProcessed[damageKey] = nil
                        end)

                        -- Check if it was a headshot or body shot
                        local hasBeenDamaged, damageComponent = GetPedLastDamageBone(playerPed)

                        if hasBeenDamaged and damageComponent == 31086 then -- Head bone
                            -- This was a headshot - set health to take exactly 99 damage
                            -- This ensures 2 headshots leave player at 1 HP (200 - 99 - 99 = 2, but we'll set minimum to 1)
                            local newHealth = math.max(1, lastHealth - 99)
                            SetEntityHealth(playerPed, newHealth)
                            print('[X2HEADDY] Headshot detected - Applied 99 damage (was ' .. (lastHealth - currentHealth) .. ')')
                        else
                            -- This was a body shot - restore health (no damage for body shots in headshot-only mode)
                            SetEntityHealth(playerPed, lastHealth)
                            print('[X2HEADDY] Body shot detected - No damage applied (headshot-only mode)')
                        end
                    end
                end

                lastHealthCheck[playerId] = GetEntityHealth(playerPed)
            end
        else
            Citizen.Wait(1000) -- Wait longer when not in x2 headdy mode
        end
    end
end)



-- Enhanced damage event handler for x2 headdy mode
-- This system intercepts ALL damage events and only allows headshots
AddEventHandler('gameEventTriggered', function(name, args)
    if name == 'CEventNetworkEntityDamage' and X2Headdy then -- ONLY during x2headdy rounds
        local victim = args[1]
        local attacker = args[2]
        local isDead = args[4]
        local weapon = args[5]

        -- Check if it's a deagle damage event between players
        if weapon == DeagleHash and victim and attacker and victim ~= attacker then
            local victimPed = victim
            local currentHealth = GetEntityHealth(victimPed)

            -- Only process if victim is still alive
            if currentHealth > 0 and not isDead then
                -- Check if it was a headshot
                local hasBeenDamaged, damageComponent = GetPedLastDamageBone(victimPed)

                Citizen.SetTimeout(10, function() -- Small delay to let damage register
                    local newCurrentHealth = GetEntityHealth(victimPed)

                    if newCurrentHealth > 0 then -- Still alive after damage
                        if hasBeenDamaged and damageComponent == 31086 then -- Head bone
                            -- This was a headshot - apply exactly 99 damage
                            local targetHealth = math.max(1, currentHealth - 99)
                            SetEntityHealth(victimPed, targetHealth)
                            print('[X2HEADDY] HEADSHOT: Applied 99 damage')
                        else
                            -- This was NOT a headshot - restore original health (no damage)
                            SetEntityHealth(victimPed, currentHealth)
                            print('[X2HEADDY] BODY SHOT: Blocked - No damage applied')
                        end
                    end
                end)
            end
        end
    end
end)

-- Health monitoring system for x2 headdy mode
-- This ensures only headshots can reduce health
Citizen.CreateThread(function()
    local playerHealths = {}

    while true do
        Citizen.Wait(100) -- Check every 100ms

        if X2Headdy then
            local ped = PlayerPedId()
            local currentHealth = GetEntityHealth(ped)
            local lastHealth = playerHealths[PlayerId()] or currentHealth

            -- If health decreased
            if currentHealth < lastHealth and currentHealth > 0 then
                -- Check if this was a legitimate headshot
                local hasBeenDamaged, damageComponent = GetPedLastDamageBone(ped)

                if hasBeenDamaged and damageComponent == 31086 then
                    -- This was a headshot - ensure damage is exactly 99
                    local expectedHealth = math.max(1, lastHealth - 99)
                    if math.abs(currentHealth - expectedHealth) > 5 then -- Allow small variance
                        SetEntityHealth(ped, expectedHealth)
                        print('[X2HEADDY] CORRECTED: Headshot damage adjusted to 99')
                    end
                else
                    -- This was NOT a headshot - restore health
                    SetEntityHealth(ped, lastHealth)
                    print('[X2HEADDY] BLOCKED: Body shot damage prevented')
                end
            end

            playerHealths[PlayerId()] = GetEntityHealth(ped)
        else
            playerHealths = {}
            Citizen.Wait(1000)
        end
    end
end)

-- Heal player event (triggered when player gets a kill in deagle mode)
RegisterNetEvent("deagle:healPlayer")
AddEventHandler("deagle:healPlayer", function(silent)
    local ped = PlayerPedId()
    local maxHealth = GetEntityMaxHealth(ped)

    -- Heal to max health
    SetEntityHealth(ped, maxHealth)

    -- Only show visual effect and notification if not in silent mode
    if not silent then
        notify("~g~KILL HEAL! ~w~Health restored!")
    end

    print('[DEAGLE] Player healed for getting a kill')
end)

-- Siphon heal player event (triggered when player gets a kill and siphon is enabled server-side)
RegisterNetEvent("siphon:healPlayer")
AddEventHandler("siphon:healPlayer", function()
    -- Server already checked if siphon is enabled, so just heal the player
    local ped = PlayerPedId()
    local maxHealth = GetEntityMaxHealth(ped)
    local currentHealth = GetEntityHealth(ped)
    
    -- Debug logging
    print('[SIPHON CLIENT] Heal event received')
    print('[SIPHON CLIENT] Current health: ' .. tostring(currentHealth))
    print('[SIPHON CLIENT] Max health: ' .. tostring(maxHealth))

    -- Heal to max health
    SetEntityHealth(ped, maxHealth)

    -- Debug logging
    print('[SIPHON CLIENT] Health after heal: ' .. tostring(GetEntityHealth(ped)))

    print('[SIPHON] Player healed for getting a kill (server-verified siphon enabled)')
end)

-- Notification function for deagle mode
function notify(message)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(message)
    DrawNotification(false, false)
end

-- Weapon giving prevention system
local originalGiveWeaponToPed = GiveWeaponToPed

-- Override GiveWeaponToPed to prevent non-deagle weapons during deagle only modes
GiveWeaponToPed = function(ped, weaponHash, ammoCount, isHidden, equipNow)
    -- Only apply restriction to the local player
    if ped == PlayerPedId() and (DeagleOnly or X2Headdy) then
        -- Allow only deagle and unarmed
        if weaponHash ~= DeagleHash and weaponHash ~= GetHashKey("WEAPON_UNARMED") then
            print('[DEAGLE ENFORCEMENT] Blocked weapon give: ' .. tostring(weaponHash))
            return false
        end
    end
    return originalGiveWeaponToPed(ped, weaponHash, ammoCount, isHidden, equipNow)
end

-- Aggressive weapon enforcement loop for deagle only mode and x2 headdy mode
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100) -- Check every 100ms for faster enforcement
        if DeagleOnly or X2Headdy then
            local ped = PlayerPedId()

            -- Remove ALL weapons except deagle and unarmed
            for i = 1, #ConfigWeapons do
                local weapon = GetHashKey(ConfigWeapons[i])
                if weapon ~= DeagleHash and HasPedGotWeapon(ped, weapon, false) then
                    RemoveWeaponFromPed(ped, weapon)
                    print('[DEAGLE ENFORCEMENT] Removed weapon: ' .. ConfigWeapons[i])
                end
            end

            -- Ensure player has deagle if they don't have any weapon
            local currentWeapon = GetSelectedPedWeapon(ped)
            if currentWeapon == GetHashKey("WEAPON_UNARMED") then
                if not HasPedGotWeapon(ped, DeagleHash, false) then
                    originalGiveWeaponToPed(ped, DeagleHash, 250, false, true)
                    print('[DEAGLE ENFORCEMENT] Gave deagle to player')
                end
            end
        else
            Citizen.Wait(2000) -- Wait longer when not in deagle/x2headdy mode
        end
    end
end)

-- Immediate weapon change detection and enforcement
Citizen.CreateThread(function()
    local lastWeapon = GetHashKey("WEAPON_UNARMED")

    while true do
        Citizen.Wait(0) -- Check every frame for immediate response

        if DeagleOnly or X2Headdy then
            local ped = PlayerPedId()
            local currentWeapon = GetSelectedPedWeapon(ped)

            -- If weapon changed and it's not deagle or unarmed
            if currentWeapon ~= lastWeapon then
                if currentWeapon ~= DeagleHash and currentWeapon ~= GetHashKey("WEAPON_UNARMED") then
                    -- Immediately remove the weapon and switch to deagle
                    RemoveWeaponFromPed(ped, currentWeapon)
                    if HasPedGotWeapon(ped, DeagleHash, false) then
                        SetCurrentPedWeapon(ped, DeagleHash, true)
                    else
                        originalGiveWeaponToPed(ped, DeagleHash, 250, false, true)
                        SetCurrentPedWeapon(ped, DeagleHash, true)
                    end
                    print('[DEAGLE ENFORCEMENT] Immediately blocked weapon switch to: ' .. tostring(currentWeapon))
                end
                lastWeapon = GetSelectedPedWeapon(ped)
            end
        else
            lastWeapon = GetSelectedPedWeapon(PlayerPedId())
            Citizen.Wait(500) -- Check less frequently when not in deagle mode
        end
    end
end)

-- Block ESX weapon giving during deagle only modes
AddEventHandler('esx:addInventoryItem', function(item, count, showNotification)
    if (DeagleOnly or X2Headdy) and item and type(item) == 'string' then
        -- Check if the item is a weapon
        if string.find(item:lower(), 'weapon_') and item:lower() ~= 'weapon_pistol50' then
            print('[DEAGLE ENFORCEMENT] Blocked ESX weapon give: ' .. item)
            return false
        end
    end
end)

-- Block weapon pickups during deagle only modes
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(100)

        if DeagleOnly or X2Headdy then
            local ped = PlayerPedId()
            local coords = GetEntityCoords(ped)

            -- Disable weapon pickups in a radius around the player
            local pickupHash = GetPickupHash()
            if pickupHash ~= 0 then
                local pickup = GetClosestObjectOfType(coords.x, coords.y, coords.z, 2.0, pickupHash, false, false, false)
                if pickup ~= 0 then
                    DeleteObject(pickup)
                    print('[DEAGLE ENFORCEMENT] Removed weapon pickup')
                end
            end
        else
            Citizen.Wait(1000)
        end
    end
end)

-- DISABLED: Client-side kill detection system for deagle mode healing
-- This system was causing multiple players to get healed for the same kill
-- The server-side ESX death handler (esx:onPlayerDeath) properly handles kill detection and healing
local playerHealths = {}

--[[
DISABLED: This client-side kill detection was causing the issue where multiple players
would get healed for the same kill. Each client was monitoring all players' health
and when someone died, multiple clients with deagles equipped within range would
trigger heal events for themselves.

The server-side esx:onPlayerDeath event handler in server.lua properly identifies
the actual killer and only heals that specific player.

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- Check every 500ms for better responsiveness

        if DeagleOnly then
            -- Only heal for regular deagle mode, NOT x2 headdy mode
            local players = GetActivePlayers()
            local myPlayerId = PlayerId()
            local myPed = PlayerPedId()
            local myWeapon = GetSelectedPedWeapon(myPed)

            for _, playerId in ipairs(players) do
                if playerId ~= myPlayerId then
                    local playerPed = GetPlayerPed(playerId)
                    local currentHealth = GetEntityHealth(playerPed)
                    local previousHealth = playerHealths[playerId] or currentHealth

                    -- If player died and I have deagle equipped
                    if previousHealth > 0 and currentHealth <= 0 and myWeapon == DeagleHash then
                        -- Check if I'm close enough to have potentially killed them
                        local myPos = GetEntityCoords(myPed)
                        local theirPos = GetEntityCoords(playerPed)
                        local distance = #(myPos - theirPos)

                        -- If within reasonable kill distance (100 units)
                        if distance < 100.0 then
                            -- Report kill to server (only for regular deagle mode)
                            TriggerServerEvent('deagle:playerKilled', GetPlayerServerId(myPlayerId), GetPlayerServerId(playerId))
                        end
                    end

                    playerHealths[playerId] = currentHealth
                end
            end
        elseif X2Headdy then
            -- x2 headdy mode - no healing, just weapon enforcement
            Citizen.Wait(1000) -- Less frequent checks since no healing needed
        else
            Citizen.Wait(2000) -- Wait longer when not in any special mode
        end
    end
end)
--]]

-- ═══════════════════════════════════════════════════════════════════════════════
-- CAR WARFARE SYSTEM
-- ═══════════════════════════════════════════════════════════════════════════════

local carWarfareEnabled = false

-- Car warfare toggle event
RegisterNetEvent("claiming:setCarWarfare")
AddEventHandler("claiming:setCarWarfare", function(state)
    carWarfareEnabled = state
    print('[CLAIMING] Car warfare client received: ' .. (state and 'enabled' or 'disabled'))
    print('[CLAIMING] carWarfareEnabled is now: ' .. tostring(carWarfareEnabled))
end)

-- Client-side car warfare toggle (triggered by UI button)
RegisterNetEvent('claiming:toggleCarWarfareClient')
AddEventHandler('claiming:toggleCarWarfareClient', function()
    carWarfareEnabled = not carWarfareEnabled
    print('[CLAIMING] Car warfare toggled via UI button to: ' .. tostring(carWarfareEnabled))

    -- Send chat message to player
    TriggerEvent('chat:addMessage', {
        template = '<div><b style="color:#c1a2fc">CLAIMING</b> <span style="color:' .. (carWarfareEnabled and '#97f57d' or '#ff0000') .. '">Car Warfare ' .. (carWarfareEnabled and 'enabled' or 'disabled') .. '!</span></div>'
    })

    -- Update UI button state
    SendNUIMessage({
        action = 'updateCarWarfare',
        enabled = carWarfareEnabled
    })
end)

-- Siphon system variables
local siphonEnabled = true -- Start with Siphon enabled by default

-- Client-side siphon toggle (triggered by UI button)
RegisterNetEvent('claiming:toggleSiphonClient')
AddEventHandler('claiming:toggleSiphonClient', function()
    -- Trigger server-side siphon toggle
    TriggerServerEvent('claiming:toggleSiphonServer')
    print('[CLAIMING] Siphon toggle request sent to server')
    
    -- Add immediate visual feedback while waiting for server response
    SendNUIMessage({
        action = 'updateSiphon',
        enabled = not siphonEnabled -- Toggle visual state immediately
    })
    siphonEnabled = not siphonEnabled -- Update local state
end)

-- Handle siphon state updates from server (will sync all clients)
RegisterNetEvent('claiming:setSiphon')
AddEventHandler('claiming:setSiphon', function(enabled)
    siphonEnabled = enabled
    print('[CLAIMING] Siphon state updated from server: ' .. tostring(enabled))

    -- Update UI button state
    SendNUIMessage({
        action = 'updateSiphon',
        enabled = enabled
    })
end)

-- Car warfare enforcement - prevent drivers from shooting
local lastDebugTime = 0
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)

        local ped = PlayerPedId()
        if IsPedInAnyVehicle(ped, false) then
            local vehicle = GetVehiclePedIsIn(ped, false)
            local isDriver = GetPedInVehicleSeat(vehicle, -1) == ped

            -- Debug every 5 seconds when in vehicle
            local currentTime = GetGameTimer()
            if currentTime - lastDebugTime > 5000 then
                print('[CLAIMING] In vehicle - Driver: ' .. tostring(isDriver) .. ', Car warfare enabled: ' .. tostring(carWarfareEnabled))
                lastDebugTime = currentTime
            end

            -- If car warfare is disabled and player is driver, disable shooting
            if not carWarfareEnabled and isDriver then
                DisableControlAction(0, 24, true) -- Attack
                DisableControlAction(0, 25, true) -- Aim
                DisableControlAction(0, 68, true) -- Vehicle aim
                DisableControlAction(0, 69, true) -- Vehicle attack
                DisableControlAction(0, 70, true) -- Vehicle attack 2
                DisableControlAction(0, 92, true) -- Vehicle passenger attack
                DisableControlAction(0, 114, true) -- Vehicle fly attack
                DisableControlAction(0, 331, true) -- Vehicle melee attack
            end
        else
            Citizen.Wait(500) -- Wait longer when not in vehicle
        end
    end
end)

-- ═══════════════════════════════════════════════════════════════════════════════
-- CLAIMING SYSTEM INTEGRATION
-- ═══════════════════════════════════════════════════════════════════════════════

-- Give deagle to player (called from server)
RegisterNetEvent('claiming:giveDeagle')
AddEventHandler('claiming:giveDeagle', function()
  local ped = PlayerPedId()
  local deagleHash = GetHashKey("weapon_pistol50")

  -- Remove all weapons first
  RemoveAllPedWeapons(ped, true)

  -- Give deagle with ammo
  GiveWeaponToPed(ped, deagleHash, 250, false, true)
  SetCurrentPedWeapon(ped, deagleHash, true)


end)

-- Integration with deagle_only mode
RegisterNetEvent('claiming:mode')
AddEventHandler('claiming:mode', function(mode)
  -- Always reset weapon damage first when changing modes
  resetWeaponDamage()

  -- Additional aggressive reset for non-x2headdy modes
  if mode ~= 'x2headdy' then
    Citizen.CreateThread(function()
      for i = 1, 20 do -- Reset 20 times over 2 seconds to ensure it sticks
        SetWeaponDamageModifier(DeagleHash, 1.0)
        Citizen.Wait(100)
      end
      print('[AGGRESSIVE RESET] Weapon damage forced to normal for mode: ' .. mode)
    end)
  end

  if mode == 'deagleonly' then
    -- Deagle only mode - normal 1-tap headshots
    TriggerEvent('deagle:setMode', true)
    TriggerEvent('x2headdy:setMode', false) -- Ensure x2headdy is OFF
    print('[MODE] Deagle only mode - 1-tap headshots enabled')
  elseif mode == 'x2headdy' then
    -- x2 headdy mode - special damage system
    TriggerEvent('deagle:setMode', true)
    TriggerEvent('x2headdy:setMode', true) -- Enable x2headdy
    print('[MODE] x2 Headdy mode - Headshot-only damage system enabled')
  else
    -- Any other mode - normal 1-tap headshots
    TriggerEvent('deagle:setMode', false)
    TriggerEvent('x2headdy:setMode', false) -- Ensure x2headdy is OFF
    TriggerEvent('gang_heli_garage:roundStatus', false)
    print('[MODE] Default mode - 1-tap headshots enabled')
  end
end)

-- Round end event handler to ensure weapon damage is reset
RegisterNetEvent('claiming:end')
AddEventHandler('claiming:end', function()
    -- Reset weapon damage when round ends
    resetWeaponDamage()
    X2Headdy = false -- Ensure x2headdy is disabled
    print('[ROUND END] Weapon damage reset to normal (1-tap headshots)')
end)

-- Enhanced claiming attempt with better feedback
RegisterNetEvent('claiming:attempt')
AddEventHandler('claiming:attempt', function(locKey)
  local ped = PlayerPedId()
  local coords = GetEntityCoords(ped)
  
  -- Visual feedback for claiming
  local effect = "scr_rcbarry2"
  local effectName = "scr_clown_appears"
  
  RequestNamedPtfxAsset(effect)
  while not HasNamedPtfxAssetLoaded(effect) do
    Citizen.Wait(1)
  end
  
  UseParticleFxAssetNextCall(effect)
  StartParticleFxNonLoopedAtCoord(effectName, coords.x, coords.y, coords.z, 0.0, 0.0, 0.0, 1.0, false, false, false)
  
  -- Sound effect
  PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", 1)
  
  -- Screen effect
  SetTimecycleModifier("spectator5")
  Citizen.SetTimeout(2000, function()
    ClearTimecycleModifier()
  end)
end)

-- Enhanced round start with proper mode setup
RegisterNetEvent('claiming:start')
AddEventHandler('claiming:start', function(locKey, coords, mode)
  -- Store the mode for this round
  currentMode = mode or 'any'
  
  -- Apply mode-specific setup
  if currentMode == 'deagleonly' then
    Citizen.CreateThread(function()
      Citizen.Wait(2000) -- Give time for round to start

      local ped = PlayerPedId()
      local deagleHash = GetHashKey("weapon_pistol50")

      -- Ensure player has deagle
      if not HasPedGotWeapon(ped, deagleHash, false) then
        RemoveAllPedWeapons(ped, true)
        GiveWeaponToPed(ped, deagleHash, 250, false, true)
        SetCurrentPedWeapon(ped, deagleHash, true)
      end
    end)
  elseif currentMode == 'x2headdy' then
    Citizen.CreateThread(function()
      Citizen.Wait(2000) -- Give time for round to start

      local ped = PlayerPedId()
      local deagleHash = GetHashKey("weapon_pistol50")

      -- Ensure player has deagle
      if not HasPedGotWeapon(ped, deagleHash, false) then
        RemoveAllPedWeapons(ped, true)
        GiveWeaponToPed(ped, deagleHash, 250, false, true)
        SetCurrentPedWeapon(ped, deagleHash, true)
      end
    end)
  end
end)

-- Handle new players joining during x2 headdy rounds
RegisterNetEvent('claiming:start')
AddEventHandler('claiming:start', function(data)
  currentMode = data.mode

  -- Apply mode-specific setup
  if currentMode == 'x2headdy' then
    Citizen.CreateThread(function()
      Citizen.Wait(1000) -- Give time for mode to activate

      -- Initialize headshot-only mode for new player
      if X2Headdy then
        print('[X2HEADDY] Player joined during headshot-only mode')
      end
    end)
  end
end)

-- Enhanced round end cleanup
RegisterNetEvent('claiming:end')
AddEventHandler('claiming:end', function()
  -- Disable all special modes
  TriggerEvent('deagle:setMode', false)
  TriggerEvent('x2headdy:setMode', false)
  TriggerEvent('gang_heli_garage:roundStatus', false)

  -- Clear any visual effects
  ClearTimecycleModifier()

  print('[X2HEADDY] Round ended - headshot-only mode disabled')
end)

-- Additional weapon enforcement for claiming rounds (legacy - now handled by main deagle system above)
-- This is kept for compatibility but the main enforcement is handled by the DeagleOnly system

-- Enhanced claiming marker with better visuals
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    
    if active or paused then
      local ped = PlayerPedId()
      local coords = GetEntityCoords(ped)
      local dist = #(coords - locCoords)
      
      if dist < 50.0 then
        -- Enhanced marker with pulsing effect
        local time = GetGameTimer() / 1000.0
        local pulse = math.sin(time * 3.0) * 0.2 + 1.0
        local color = localClaimed and {0, 255, 0} or {255, 0, 0}
        
        DrawMarker(
          28, -- Marker type (cylinder)
          locCoords.x, locCoords.y, locCoords.z - 0.98,
          0, 0, 0, 0, 0, 0,
          2.0 * pulse, 2.0 * pulse, 1.0,
          color[1], color[2], color[3], 100,
          false, true, 2, nil, nil, false
        )
        
        -- Light effect
        DrawLightWithRange(locCoords.x, locCoords.y, locCoords.z + 2.0, color[1], color[2], color[3], 10.0, 1.0)
        
        if dist < 3.0 then
          -- Enhanced 3D text
          local text = localClaimed and "~g~CLAIMED!" or "[~g~G~w~] CLAIM LOCATION"
          DrawText3D(locCoords.x, locCoords.y, locCoords.z + 1.5, text)
          
          -- Interaction
          if not localClaimed and IsControlJustReleased(0, 47) then -- G key
            TriggerServerEvent('claiming:attempt', currentLoc)
          end
        end
      end
    else
      Citizen.Wait(500)
    end
  end
end)

-- Helper function for 3D text (if not already defined)
function DrawText3D(x, y, z, text)
  local onScreen, _x, _y = World3dToScreen2d(x, y, z)
  if not onScreen then return end
  
  SetTextScale(0.4, 0.4)
  SetTextFont(4)
  SetTextProportional(1)
  SetTextEntry("STRING")
  SetTextCentre(true)
  SetTextDropShadow(2, 0, 0, 0, 255)
  SetTextOutline()
  AddTextComponentSubstringPlayerName(text)
  DrawText(_x, _y)
end

-- Integration status check
Citizen.CreateThread(function()
  Citizen.Wait(5000) -- Wait for resources to load

  local integrations = {
    deagle_only = "✓ Integrated", -- Now integrated into claiming_script
    car_marker = GetResourceState('car_marker') == 'started',
    sg_spawns = GetResourceState('sg_spawns') == 'started'
  }

  local status = "Claiming Integration Status:\n"
  for name, loaded in pairs(integrations) do
    if type(loaded) == "string" then
      status = status .. string.format("- %s: %s\n", name, loaded)
    else
      status = status .. string.format("- %s: %s\n", name, loaded and "✓ Loaded" or "✗ Missing")
    end
  end

  print(status)
end)
