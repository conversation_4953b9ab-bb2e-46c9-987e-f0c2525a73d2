// Global variables
let currentPlayerData = null;
let onlinePlayers = [];
let onlineStaff = [];
let staffLogs = [];
let namesEnabled = false;

// Performance optimization variables
let updateInterval = null;
let cleanupInterval = null;
let isInitialized = false;

// DOM Elements
const staffPanel = document.getElementById('staffPanel');
const closeBtn = document.getElementById('closeBtn');
const navItems = document.querySelectorAll('.nav-item');
const sections = document.querySelectorAll('.section');
const refreshBtn = document.getElementById('refreshBtn');
const searchInput = document.getElementById('searchInput');
const welcomeMessage = document.getElementById('welcomeMessage');
const currentDate = document.getElementById('currentDate');
const staffName = document.getElementById('staffName');
const staffRole = document.getElementById('staffRole');
const totalPlayers = document.getElementById('totalPlayers');
const totalStaff = document.getElementById('totalStaff');
const onlineStaffCount = document.getElementById('onlineStaffCount');
const playersList = document.getElementById('playersList');
const onlineStaffList = document.getElementById('onlineStaffList');
const killAllBtn = document.getElementById('killAllBtn');
const reviveAllBtn = document.getElementById('reviveAllBtn');
const setTimeBtn = document.getElementById('setTimeBtn');
const setWeatherBtn = document.getElementById('setWeatherBtn');
const timeHour = document.getElementById('timeHour');
const timeMinute = document.getElementById('timeMinute');
const weatherSelect = document.getElementById('weatherSelect');

// Player Info Section elements
const playerInfoSection = document.getElementById('player-info-section');
const backToPlayersBtn = document.getElementById('backToPlayersBtn');
const playerInfoName = document.getElementById('playerInfoName');
const playerInfoId = document.getElementById('playerInfoId');
const playerInfoJob = document.getElementById('playerInfoJob');
const playerInfoJobGrade = document.getElementById('playerInfoJobGrade');
const playerInfoGroup = document.getElementById('playerInfoGroup');
const playerInfoMoney = document.getElementById('playerInfoMoney');
const playerInfoBank = document.getElementById('playerInfoBank');
const playerInfoIdentifier = document.getElementById('playerInfoIdentifier');

let currentSelectedPlayer = null;
const toggleNamesBtn = document.getElementById('toggleNamesBtn');
const namesButtonText = document.getElementById('namesButtonText');
const logsList = document.getElementById('logsList');
const logTypeFilter = document.getElementById('logTypeFilter');
const clearLogsBtn = document.getElementById('clearLogsBtn');
const serverUptime = document.getElementById('serverUptime');
const recentReports = document.getElementById('recentReports');

// Item dropdown elements
const itemSearchInput = document.getElementById('itemSearchInput');
const itemDropdown = document.getElementById('itemDropdown');
const itemQuantityInput = document.getElementById('itemQuantityInput');
const itemTargetSelect = document.getElementById('itemTargetSelect');
const giveItemBtn = document.getElementById('giveItemBtn');
const loadItemsBtn = document.getElementById('loadItemsBtn');

// Item data
let availableItems = [];
let selectedItem = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (isInitialized) return; // Prevent double initialization

    console.log('[Sapphire Gaming Staff Panel] DOM loaded, initializing...');
    console.log('[Sapphire Gaming Staff Panel] staffPanel element:', !!staffPanel);

    // Clear any existing intervals
    if (updateInterval) clearInterval(updateInterval);
    if (cleanupInterval) clearInterval(cleanupInterval);

    updateDateTime();
    updateInterval = setInterval(updateDateTime, 5000); // Reduced frequency

    // Hide all admin-only elements by default until permission check
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    adminOnlyElements.forEach(element => {
        element.style.display = 'none';
        element.classList.add('hidden');
    });
    console.log('[Sapphire Gaming Staff Panel] Hidden', adminOnlyElements.length, 'admin-only elements by default');

    // Navigation
    navItems.forEach(item => {
        item.addEventListener('click', () => {
            const section = item.dataset.section;
            switchSection(section);
        });
    });
    
    // Close button
    closeBtn.addEventListener('click', closePanel);
    
    // Refresh button
    refreshBtn.addEventListener('click', refreshData);
    
    // Search functionality with debouncing
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(filterPlayers, 300); // Debounce search
    });
    
    // Admin-only features
    killAllBtn.addEventListener('click', killAllPlayers);
    reviveAllBtn.addEventListener('click', reviveAllPlayers);
    setTimeBtn.addEventListener('click', setServerTime);
    setWeatherBtn.addEventListener('click', setServerWeather);

    // Player info section
    if (backToPlayersBtn) {
        backToPlayersBtn.addEventListener('click', backToPlayersList);
    } else {
        console.error('backToPlayersBtn not found');
    }

    // Names toggle
    toggleNamesBtn.addEventListener('click', toggleNames);

    // Log filter
    logTypeFilter.addEventListener('change', filterLogs);

    // Clear logs
    clearLogsBtn.addEventListener('click', clearLogs);

    // Item dropdown functionality
    setupItemDropdown();

    // Give item button
    giveItemBtn.addEventListener('click', giveItemToPlayer);

    // Load items button
    loadItemsBtn.addEventListener('click', function() {
        console.log('[SG_STAFF] Manually loading items...');

        // Show loading state
        loadItemsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        loadItemsBtn.disabled = true;
        itemDropdown.innerHTML = '<div class="dropdown-no-results">Loading items...</div>';

        fetch(`https://${GetParentResourceName()}/getInventoryItems`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        // Reset button after 3 seconds
        setTimeout(() => {
            loadItemsBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Load Items';
            loadItemsBtn.disabled = false;
        }, 3000);
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);

    // Set up periodic cleanup with reduced frequency
    cleanupInterval = setInterval(() => {
        cleanupDashboard();
    }, 10000); // Check every 10 seconds instead of 1

    isInitialized = true;

    console.log('[Sapphire Gaming Staff Panel] Initialization complete');
});

// Update date and time
function updateDateTime() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    currentDate.textContent = now.toLocaleDateString('en-US', options);
}

// Switch sections
function switchSection(sectionName) {
    // Special handling for player-info section (no nav item)
    if (sectionName === 'player-info') {
        // Hide all sections
        sections.forEach(section => {
            section.classList.remove('active');
        });
        // Show player-info section
        const playerInfoSection = document.getElementById('player-info-section');
        if (playerInfoSection) {
            playerInfoSection.classList.add('active');
        }
        // Don't update navigation for player-info
        return;
    }

    // Update navigation for regular sections
    navItems.forEach(item => {
        item.classList.remove('active');
        if (item.dataset.section === sectionName) {
            item.classList.add('active');
        }
    });

    // Update sections
    sections.forEach(section => {
        section.classList.remove('active');
        if (section.id === sectionName + '-section') {
            section.classList.add('active');
        }
    });
}

// Handle keyboard shortcuts
function handleKeyboard(e) {
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        searchInput.focus();
    }

    if (e.key === 'Escape') {
        closePanel();
    }

    // Emergency close with Ctrl+Escape
    if (e.ctrlKey && e.key === 'Escape') {
        e.preventDefault();
        staffPanel.classList.add('hidden');
        fetch(`https://${GetParentResourceName()}/closePanel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        }).catch(() => {
            // Force close even if fetch fails
            console.log('Emergency panel close');
        });
    }
}

// Filter players
function filterPlayers() {
    const query = searchInput.value.toLowerCase();
    const playerItems = document.querySelectorAll('.player-item');
    
    playerItems.forEach(item => {
        const playerName = item.querySelector('.player-details h5').textContent.toLowerCase();
        const playerId = item.querySelector('.player-details p').textContent.toLowerCase();
        
        if (playerName.includes(query) || playerId.includes(query)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// Refresh data
function refreshData() {
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';
    
    fetch(`https://${GetParentResourceName()}/refreshData`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    }).then(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
        }, 1000);
    });
}

// Admin-only functions
function killAllPlayers() {
    // Check if user has admin permissions
    const userGroup = currentPlayerData?.group?.toLowerCase() || '';
    if (!currentPlayerData || !['god', 'leadership', 'cl', 'senioradmin', 'admin'].includes(userGroup)) {
        console.log('Access denied: Kill All requires Admin+ permissions. User group:', userGroup);
        showNotification('Access denied: Admin+ permissions required', 'error');
        return;
    }

    // Disable button and execute immediately
    killAllBtn.disabled = true;
    killAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Killing All...';

    fetch(`https://${GetParentResourceName()}/killAllPlayers`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    }).finally(() => {
        setTimeout(() => {
            killAllBtn.disabled = false;
            killAllBtn.innerHTML = '<i class="fas fa-skull-crossbones"></i> Kill All';
        }, 2000);
    });
}

function reviveAllPlayers() {
    // Check if user has admin permissions
    const userGroup = currentPlayerData?.group?.toLowerCase() || '';
    if (!currentPlayerData || !['god', 'leadership', 'cl', 'senioradmin', 'admin'].includes(userGroup)) {
        console.log('Access denied: Heal All requires Admin+ permissions. User group:', userGroup);
        showNotification('Access denied: Admin+ permissions required', 'error');
        return;
    }

    // Disable button and execute immediately
    reviveAllBtn.disabled = true;
    reviveAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Healing All...';

    fetch(`https://${GetParentResourceName()}/reviveAllPlayers`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    }).finally(() => {
        setTimeout(() => {
            reviveAllBtn.disabled = false;
            reviveAllBtn.innerHTML = '<i class="fas fa-heart"></i> Heal All';
        }, 2000);
    });
}

function setServerTime() {
    // Check if user has admin permissions
    const userGroup = currentPlayerData?.group?.toLowerCase() || '';
    if (!currentPlayerData || !['god', 'leadership', 'cl', 'senioradmin', 'admin'].includes(userGroup)) {
        console.log('Access denied: Time change requires Admin+ permissions. User group:', userGroup);
        showNotification('Access denied: Admin+ permissions required', 'error');
        return;
    }

    const hour = parseInt(timeHour.value);
    const minute = parseInt(timeMinute.value);

    if (isNaN(hour) || hour < 0 || hour > 23) {
        console.log('Please enter a valid hour (0-23)');
        timeHour.style.borderColor = 'rgba(239, 68, 68, 0.5)';
        setTimeout(() => timeHour.style.borderColor = '', 2000);
        return;
    }

    if (isNaN(minute) || minute < 0 || minute > 59) {
        console.log('Please enter a valid minute (0-59)');
        timeMinute.style.borderColor = 'rgba(239, 68, 68, 0.5)';
        setTimeout(() => timeMinute.style.borderColor = '', 2000);
        return;
    }

    // Disable button to prevent multiple clicks
    setTimeBtn.disabled = true;
    setTimeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Setting...';

    fetch(`https://${GetParentResourceName()}/setServerTime`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ hour: hour, minute: minute })
    }).then(() => {
        timeHour.value = '';
        timeMinute.value = '';
        console.log(`Server time set to ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`);
    }).catch(error => {
        console.error('Error setting time:', error);
    }).finally(() => {
        // Re-enable button
        setTimeBtn.disabled = false;
        setTimeBtn.innerHTML = '<i class="fas fa-clock"></i> Set Time';
    });
}

function setServerWeather() {
    // Check if user has admin permissions
    const userGroup = currentPlayerData?.group?.toLowerCase() || '';
    if (!currentPlayerData || !['god', 'leadership', 'cl', 'senioradmin', 'admin'].includes(userGroup)) {
        console.log('Access denied: Weather change requires Admin+ permissions. User group:', userGroup);
        showNotification('Access denied: Admin+ permissions required', 'error');
        return;
    }

    const weather = weatherSelect.value;

    if (!weather) {
        console.log('Please select a weather type');
        weatherSelect.style.borderColor = 'rgba(239, 68, 68, 0.5)';
        setTimeout(() => weatherSelect.style.borderColor = '', 2000);
        return;
    }

    // Disable button to prevent multiple clicks
    setWeatherBtn.disabled = true;
    setWeatherBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Setting...';

    fetch(`https://${GetParentResourceName()}/setServerWeather`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ weather: weather })
    }).then(() => {
        console.log(`Server weather set to ${weather}`);
    }).catch(error => {
        console.error('Error setting weather:', error);
    }).finally(() => {
        // Re-enable button
        setWeatherBtn.disabled = false;
        setWeatherBtn.innerHTML = '<i class="fas fa-cloud-sun"></i> Set Weather';
    });
}

// Toggle names
function toggleNames() {
    fetch(`https://${GetParentResourceName()}/toggleNames`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });
}

// Update names status
function updateNamesStatus(enabled) {
    namesEnabled = enabled;
    const icon = toggleNamesBtn.querySelector('i');

    if (enabled) {
        toggleNamesBtn.classList.add('names-enabled');
        icon.className = 'fas fa-toggle-on';
        namesButtonText.textContent = 'Disable Names';
    } else {
        toggleNamesBtn.classList.remove('names-enabled');
        icon.className = 'fas fa-toggle-off';
        namesButtonText.textContent = 'Enable Names';
    }
}

// Update logs
function updateLogs(logs) {
    staffLogs = logs;
    renderLogs();
}

// Render logs
function renderLogs() {
    const filteredLogs = filterLogsByType();
    logsList.innerHTML = '';

    if (filteredLogs.length === 0) {
        logsList.innerHTML = `
            <div class="log-item">
                <div class="log-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="log-content">
                    <p>No logs available</p>
                    <span class="log-time">No activity recorded</span>
                </div>
            </div>
        `;
        return;
    }

    filteredLogs.forEach(log => {
        const logItem = document.createElement('div');
        logItem.className = 'log-item';

        const iconClass = getLogIconClass(log.type);

        logItem.innerHTML = `
            <div class="log-icon ${log.type.toLowerCase()}">
                <i class="${iconClass}"></i>
            </div>
            <div class="log-content">
                <p>${log.message}</p>
                <span class="log-time">${log.timestamp}</span>
            </div>
        `;

        logsList.appendChild(logItem);
    });
}

// Filter logs by type
function filterLogsByType() {
    const selectedType = logTypeFilter.value;
    if (selectedType === 'all') {
        return staffLogs;
    }
    return staffLogs.filter(log => log.type === selectedType);
}

// Filter logs
function filterLogs() {
    renderLogs();
}

// Clear logs
function clearLogs() {
    // Simple double-click confirmation
    if (clearLogsBtn.dataset.confirmClear === 'true') {
        staffLogs = [];
        renderLogs();
        clearLogsBtn.dataset.confirmClear = 'false';
        clearLogsBtn.innerHTML = '<i class="fas fa-trash"></i> Clear Logs';
        console.log('Logs cleared');
    } else {
        clearLogsBtn.dataset.confirmClear = 'true';
        clearLogsBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Click Again to Confirm';
        clearLogsBtn.style.background = 'rgba(239, 68, 68, 0.2)';

        setTimeout(() => {
            clearLogsBtn.dataset.confirmClear = 'false';
            clearLogsBtn.innerHTML = '<i class="fas fa-trash"></i> Clear Logs';
            clearLogsBtn.style.background = '';
        }, 3000);
    }
}

// Get log icon class
function getLogIconClass(type) {
    const icons = {
        'NAMES': 'fas fa-eye',
        'KICK': 'fas fa-sign-out-alt',
        'BAN': 'fas fa-ban',
        'TELEPORT': 'fas fa-location-arrow',
        'BRING': 'fas fa-hand-paper',
        'KILL_ALL': 'fas fa-skull-crossbones',
        'REVIVE_ALL': 'fas fa-heart',
        'TIME_CHANGE': 'fas fa-clock',
        'WEATHER_CHANGE': 'fas fa-cloud-sun',
        'CLEAR_INV': 'fas fa-trash'
    };
    return icons[type] || 'fas fa-info-circle';
}

// Update server stats
function updateServerStats(stats) {
    totalPlayers.textContent = stats.onlinePlayers;
    totalStaff.textContent = stats.staffOnline;
    recentReports.textContent = stats.recentReports;

    // Format uptime
    const hours = Math.floor(stats.uptime / 3600);
    const minutes = Math.floor((stats.uptime % 3600) / 60);
    const seconds = stats.uptime % 60;
    serverUptime.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// Close panel
function closePanel() {
    // Clear intervals to prevent memory leaks
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
    if (cleanupInterval) {
        clearInterval(cleanupInterval);
        cleanupInterval = null;
    }

    // Reset initialization flag
    isInitialized = false;

    // Immediately hide the panel
    staffPanel.classList.add('hidden');

    // Try to notify the client, but don't wait for it
    fetch(`https://${GetParentResourceName()}/closePanel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.log('Panel closed locally due to error:', error);
    });
}

// Player actions (kick and ban moved to player info modal)

function teleportToPlayer(playerId) {
    fetch(`https://${GetParentResourceName()}/teleportToPlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ playerId: playerId })
    });
}

function bringPlayer(playerId) {
    fetch(`https://${GetParentResourceName()}/bringPlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ playerId: playerId })
    });
}

// Update player data
function updatePlayerData(data) {
    currentPlayerData = data;
    welcomeMessage.textContent = `Welcome back, ${data.name}`;
    staffName.textContent = data.name;
    staffRole.textContent = data.group.charAt(0).toUpperCase() + data.group.slice(1);

    console.log('[Sapphire Gaming Staff Panel] Player data received:', {
        name: data.name,
        group: data.group,
        groupType: typeof data.group
    });

    // Show/hide admin-only features based on group
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    const userGroup = data.group.toLowerCase(); // Ensure lowercase comparison
    const isAdmin = ['god', 'leadership', 'cl', 'senioradmin', 'admin'].includes(userGroup);

    console.log('Permission check - User group:', userGroup, 'Is Admin:', isAdmin); // Debug log

    adminOnlyElements.forEach(element => {
        if (isAdmin) {
            element.classList.add('show');
            element.classList.remove('hidden');
            element.style.display = 'flex';
        } else {
            element.classList.remove('show');
            element.classList.add('hidden');
            element.style.display = 'none';
        }
    });

    console.log('Player data updated:', data); // Debug log
}

// Update online players
function updateOnlinePlayers(players) {
    onlinePlayers = players;
    renderPlayers();
    renderOnlineStaff();
    updateItemTargetSelect(players);
}

// Render players
function renderPlayers() {
    playersList.innerHTML = '';

    if (onlinePlayers.length === 0) {
        playersList.innerHTML = `
            <div class="player-item">
                <div class="player-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="player-details">
                    <h5>No players online</h5>
                    <p>Server is empty</p>
                </div>
            </div>
        `;
        return;
    }

    onlinePlayers.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';
        playerItem.onclick = () => showPlayerInfo(player);

        const isStaff = ['god', 'leadership', 'cl', 'senioradmin', 'admin', 'mod'].includes(player.group);
        const staffBadge = isStaff ? `<div class="status-badge staff">${player.group.toUpperCase()}</div>` : '';

        playerItem.innerHTML = `
            <div class="player-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="player-details">
                <h5>${player.name}</h5>
                <p>ID: ${player.source} | Job: ${player.job}</p>
                <div class="player-status">
                    <div class="status-badge online">Online</div>
                    ${staffBadge}
                </div>
            </div>
        `;

        playersList.appendChild(playerItem);
    });
}

// Render online staff
function renderOnlineStaff() {
    onlineStaffList.innerHTML = '';

    // Remove any unwanted "Online Staff" sections from dashboard - be more aggressive
    const dashboardSection = document.getElementById('dashboard-section');
    if (dashboardSection) {
        // Find and remove any h4 elements with "Online Staff" text in dashboard
        const h4Elements = dashboardSection.querySelectorAll('h4');
        h4Elements.forEach(h4 => {
            if (h4.textContent && h4.textContent.trim() === 'Online Staff') {
                console.log('Removing unwanted Online Staff h4 from dashboard:', h4);
                // Remove the parent container
                if (h4.parentElement) {
                    h4.parentElement.remove();
                } else {
                    h4.remove();
                }
            }
        });
    }

    const staffMembers = onlinePlayers.filter(player =>
        ['god', 'leadership', 'cl', 'senioradmin', 'admin', 'mod'].includes(player.group)
    );

    onlineStaffCount.textContent = staffMembers.length;

    if (staffMembers.length === 0) {
        onlineStaffList.innerHTML = `
            <div class="staff-member">
                <div class="staff-member-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="staff-member-info">
                    <h6>No staff online</h6>
                    <p>Offline</p>
                </div>
            </div>
        `;
        return;
    }

    // Sort staff by level (god first, then leadership, etc.)
    staffMembers.sort((a, b) => {
        const levels = { 'god': 0, 'leadership': 1, 'cl': 2, 'senioradmin': 3, 'admin': 4, 'mod': 5 };
        return (levels[a.group] || 999) - (levels[b.group] || 999);
    });

    staffMembers.forEach(staff => {
        const staffItem = document.createElement('div');
        staffItem.className = 'staff-member';

        const nameClass = staff.hasNamesEnabled ? 'staff-name-red' : '';

        staffItem.innerHTML = `
            <div class="staff-member-avatar">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="staff-member-info">
                <h6 class="${nameClass}">${staff.name}</h6>
                <p>${staff.group.toUpperCase()}</p>
            </div>
        `;

        onlineStaffList.appendChild(staffItem);
    });
}

// Show player info
function showPlayerInfo(player) {
    currentSelectedPlayer = player;

    playerInfoName.textContent = player.name;
    playerInfoId.textContent = `ID: ${player.source}`;
    playerInfoJob.textContent = player.job;
    playerInfoJobGrade.textContent = player.jobGrade;
    playerInfoGroup.textContent = player.group;
    playerInfoMoney.textContent = `$${player.money.toLocaleString()}`;
    playerInfoBank.textContent = `$${player.bank.toLocaleString()}`;
    playerInfoIdentifier.textContent = player.identifier;

    switchSection('player-info');
}

// Back to players list
function backToPlayersList() {
    currentSelectedPlayer = null;
    switchSection('players');
}

// Player action functions for info modal
function teleportToPlayerFromInfo() {
    if (currentSelectedPlayer) {
        teleportToPlayer(currentSelectedPlayer.source);
    }
}

function bringPlayerFromInfo() {
    if (currentSelectedPlayer) {
        bringPlayer(currentSelectedPlayer.source);
    }
}

function kickPlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    const reason = prompt('Enter kick reason:');
    if (reason) {
        fetch(`https://${GetParentResourceName()}/txBansKick`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                playerId: currentSelectedPlayer.source,
                reason: reason
            })
        });
        backToPlayersList();
    }
}

function banPlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    const reason = prompt('Enter ban reason:');
    if (!reason) return;

    const duration = prompt('Enter ban duration in hours (24 = 1 day):');
    if (!duration || isNaN(duration)) return;

    const durationSeconds = parseInt(duration) * 3600;

    fetch(`https://${GetParentResourceName()}/txBansBan`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            playerId: currentSelectedPlayer.source,
            reason: reason,
            duration: durationSeconds
        })
    });
    backToPlayersList();
}

function clearPlayerInventory() {
    if (!currentSelectedPlayer) return;

    if (confirm(`Are you sure you want to clear ${currentSelectedPlayer.name}'s inventory?`)) {
        fetch(`https://${GetParentResourceName()}/clearInventory`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                playerId: currentSelectedPlayer.source
            })
        });
    }
}

// Additional player action functions
function warnPlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    const reason = prompt('Enter warning reason:');
    if (reason) {
        fetch(`https://${GetParentResourceName()}/warnPlayer`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentSelectedPlayer.source,
                reason: reason
            })
        });
    }
}

function spectatePlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    fetch(`https://${GetParentResourceName()}/spectatePlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: currentSelectedPlayer.source
        })
    });

    closeStaffPanel();
}

function screenshotPlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    if (confirm(`Take a screenshot of ${currentSelectedPlayer.name}?`)) {
        fetch(`https://${GetParentResourceName()}/screenshotPlayer`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentSelectedPlayer.source
            })
        });
    }
}

function freezePlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    fetch(`https://${GetParentResourceName()}/freezePlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: currentSelectedPlayer.source
        })
    });
}

function revivePlayerFromInfo() {
    if (!currentSelectedPlayer) return;

    fetch(`https://${GetParentResourceName()}/revivePlayer`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: currentSelectedPlayer.source
        })
    });
}

function giveMoneyFromInfo() {
    if (!currentSelectedPlayer) return;

    const accountType = document.getElementById('moneyAccountType').value;
    const amount = parseInt(document.getElementById('moneyAmount').value);

    if (!amount || amount < 1) {
        alert('Please enter a valid amount');
        return;
    }

    fetch(`https://${GetParentResourceName()}/giveMoney`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: currentSelectedPlayer.source,
            accountType: accountType,
            amount: amount
        })
    });

    document.getElementById('moneyAmount').value = '';
}

function takeMoneyFromInfo() {
    if (!currentSelectedPlayer) return;

    const accountType = document.getElementById('moneyAccountType').value;
    const amount = parseInt(document.getElementById('moneyAmount').value);

    if (!amount || amount < 1) {
        alert('Please enter a valid amount');
        return;
    }

    if (confirm(`Take $${amount} (${accountType}) from ${currentSelectedPlayer.name}?`)) {
        fetch(`https://${GetParentResourceName()}/takeMoney`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentSelectedPlayer.source,
                accountType: accountType,
                amount: amount
            })
        });

        document.getElementById('moneyAmount').value = '';
    }
}

function viewPlayerWarnings() {
    if (!currentSelectedPlayer) return;

    fetch(`https://${GetParentResourceName()}/getPlayerWarnings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: currentSelectedPlayer.source
        })
    });
}

function viewPlayerNotes() {
    if (!currentSelectedPlayer) return;

    // This would open a modal or new section to view player notes
    alert('Player notes feature - to be implemented');
}

function addPlayerNote() {
    if (!currentSelectedPlayer) return;

    const note = prompt(`Add a note for ${currentSelectedPlayer.name}:`);
    if (note) {
        fetch(`https://${GetParentResourceName()}/addPlayerNote`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                targetId: currentSelectedPlayer.source,
                note: note
            })
        });
    }
}

// NUI Message Handlers
window.addEventListener('message', function(event) {
    const data = event.data;

    switch(data.type) {
        case 'openPanel':
            console.log('[Sapphire Gaming Staff Panel] Opening panel...');
            staffPanel.classList.remove('hidden');

            // Auto-load items when panel opens
            setTimeout(() => {
                console.log('[Sapphire Gaming Staff Panel] Auto-loading items...');
                fetch(`https://${GetParentResourceName()}/getInventoryItems`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                });
            }, 1000);
            break;

        case 'closePanel':
            console.log('[Sapphire Gaming Staff Panel] Closing panel...');
            staffPanel.classList.add('hidden');
            break;

        case 'updatePlayerData':
            console.log('[Sapphire Gaming Staff Panel] Updating player data:', data.data);
            updatePlayerData(data.data);
            break;

        case 'updateOnlinePlayers':
            console.log('[Sapphire Gaming Staff Panel] Updating online players:', data.data.length);
            updateOnlinePlayers(data.data);
            break;

        case 'updateLogs':
            console.log('[Sapphire Gaming Staff Panel] Updating logs:', data.data.length);
            updateLogs(data.data);
            break;

        case 'updateServerStats':
            console.log('[Sapphire Gaming Staff Panel] Updating server stats:', data.data);
            updateServerStats(data.data);
            break;

        case 'updateNamesStatus':
            console.log('[Sapphire Gaming Staff Panel] Updating names status:', data.enabled);
            updateNamesStatus(data.enabled);
            break;

        case 'receiveInventoryItems':
            console.log('[Sapphire Gaming Staff Panel] Received inventory items:', data.data.length);
            console.log('[Sapphire Gaming Staff Panel] Items data:', data.data);
            availableItems = data.data;

            // If we have items, show a sample in the dropdown
            if (availableItems.length > 0) {
                renderItemDropdown(availableItems.slice(0, 10)); // Show first 10 items as preview
            }
            break;

        default:
            console.log('[Sapphire Gaming Staff Panel] Unknown message type:', data.type);
            break;
    }
});

// Clean up any unwanted content in dashboard
function cleanupDashboard() {
    const dashboardSection = document.getElementById('dashboard-section');
    if (dashboardSection) {
        // Remove any elements that contain "Online Staff" text but are not in the sidebar
        const allElements = dashboardSection.querySelectorAll('*');
        allElements.forEach(element => {
            if (element.textContent && element.textContent.trim() === 'Online Staff' &&
                !element.closest('.online-staff-sidebar') &&
                element.tagName !== 'SPAN') { // Don't remove the header count
                console.log('Removing unwanted Online Staff element:', element);
                // Remove the parent container if it's a heading
                if (element.tagName === 'H4' && element.parentElement) {
                    element.parentElement.remove();
                } else {
                    element.remove();
                }
            }
        });

        // Also remove any divs that might contain online staff content in dashboard
        const possibleStaffContainers = dashboardSection.querySelectorAll('div');
        possibleStaffContainers.forEach(container => {
            const h4 = container.querySelector('h4');
            if (h4 && h4.textContent && h4.textContent.trim() === 'Online Staff') {
                console.log('Removing unwanted Online Staff container:', container);
                container.remove();
            }
        });
    }
}

// Cleanup function moved to main initialization to prevent duplicates

// Item dropdown functionality
function setupItemDropdown() {
    // Show loading state initially
    itemDropdown.innerHTML = '<div class="dropdown-no-results">Loading items...</div>';

    // Request items from server
    fetch(`https://${GetParentResourceName()}/getInventoryItems`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    });

    // Search input functionality with debouncing
    let itemSearchTimeout;
    itemSearchInput.addEventListener('input', function() {
        clearTimeout(itemSearchTimeout);
        itemSearchTimeout = setTimeout(() => {
            const searchTerm = this.value.toLowerCase();
            console.log('[SG_STAFF] Searching for:', searchTerm, 'in', availableItems.length, 'items');

            if (searchTerm.length > 0) {
                filterItems(searchTerm);
                itemDropdown.classList.add('show');
            } else {
                // Show all items when no search term
                renderItemDropdown(availableItems.slice(0, 20)); // Show first 20 items
                itemDropdown.classList.add('show');
            }
        }, 300); // 300ms debounce
    });

    // Focus and blur events
    itemSearchInput.addEventListener('focus', function() {
        console.log('[SG_STAFF] Input focused, available items:', availableItems.length);
        if (this.value.length > 0) {
            filterItems(this.value.toLowerCase());
        } else {
            // Show first 20 items when focused with no search term
            renderItemDropdown(availableItems.slice(0, 20));
        }
        itemDropdown.classList.add('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.item-search-container')) {
            itemDropdown.classList.remove('show');
        }
    });

    // Keyboard navigation
    itemSearchInput.addEventListener('keydown', function(e) {
        const items = itemDropdown.querySelectorAll('.dropdown-item:not(.dropdown-no-results)');
        const selected = itemDropdown.querySelector('.dropdown-item.selected');
        let selectedIndex = -1;

        if (selected) {
            selectedIndex = Array.from(items).indexOf(selected);
        }

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (selectedIndex < items.length - 1) {
                    if (selected) selected.classList.remove('selected');
                    items[selectedIndex + 1].classList.add('selected');
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (selectedIndex > 0) {
                    if (selected) selected.classList.remove('selected');
                    items[selectedIndex - 1].classList.add('selected');
                }
                break;
            case 'Enter':
                e.preventDefault();
                if (selected) {
                    selectItem(selected.dataset.item);
                }
                break;
            case 'Escape':
                itemDropdown.classList.remove('show');
                break;
        }
    });
}

function filterItems(searchTerm) {
    console.log('[SG_STAFF] Filtering items with term:', searchTerm);
    const filteredItems = availableItems.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.label.toLowerCase().includes(searchTerm)
    );
    console.log('[SG_STAFF] Found', filteredItems.length, 'matching items');

    renderItemDropdown(filteredItems);
}

function renderItemDropdown(items) {
    itemDropdown.innerHTML = '';

    if (items.length === 0) {
        itemDropdown.innerHTML = '<div class="dropdown-no-results">No items found</div>';
        return;
    }

    items.forEach(item => {
        const dropdownItem = document.createElement('div');
        dropdownItem.className = 'dropdown-item';
        dropdownItem.dataset.item = item.name;

        dropdownItem.innerHTML = `
            <span class="item-name">${item.label}</span>
            <span class="item-weight">${item.weight}g</span>
        `;

        dropdownItem.addEventListener('click', () => selectItem(item.name));
        itemDropdown.appendChild(dropdownItem);
    });
}

function selectItem(itemName) {
    const item = availableItems.find(i => i.name === itemName);
    if (item) {
        selectedItem = item;
        itemSearchInput.value = item.label;
        itemDropdown.classList.remove('show');

        // Focus on quantity input
        itemQuantityInput.focus();
    }
}

function giveItemToPlayer() {
    if (!selectedItem) {
        alert('Please select an item first');
        return;
    }

    const quantity = parseInt(itemQuantityInput.value);
    const targetId = itemTargetSelect.value;

    if (!quantity || quantity < 1) {
        alert('Please enter a valid quantity');
        return;
    }

    if (!targetId) {
        alert('Please select a target player');
        return;
    }

    fetch(`https://${GetParentResourceName()}/giveItem`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            targetId: parseInt(targetId),
            itemName: selectedItem.name,
            quantity: quantity
        })
    });

    // Reset form
    itemSearchInput.value = '';
    itemQuantityInput.value = '1';
    selectedItem = null;
}

// Update online players for item target select
function updateItemTargetSelect(players) {
    itemTargetSelect.innerHTML = '<option value="">Select Player</option>';

    players.forEach(player => {
        const option = document.createElement('option');
        option.value = player.source;
        option.textContent = `${player.name} (ID: ${player.source})`;
        itemTargetSelect.appendChild(option);
    });
}

// Utility function for resource name
function GetParentResourceName() {
    return 'sg_staff';
}
