-- client.lua

ESX = nil

-- Fetch ESX on startup
Citizen.CreateThread(function()
    while ESX == nil do
        TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
        Citizen.Wait(10)
    end
end)

-- Register /job command
RegisterCommand('job', function()
    if not ESX then return end

    local playerData = ESX.GetPlayerData()
    local jobLabel   = playerData.job and playerData.job.label       or 'Unemployed'
    local gradeLabel = playerData.job and playerData.job.grade_label or ''

    TriggerEvent('chat:addMessage', {
        template = '<div style="padding:0.25vw; font-weight:bold;">' ..
                   '<span style="color:white;">Your Job is </span>' ..
                   '<span style="color:#5DADE2;">{0} {1}</span>' ..
                   '</div>',
        args = { jobLabel, gradeLabel }
    })
end, false)

-- Add /job to chat suggestions
TriggerEvent('chat:addSuggestion', '/job', 'Displays your current ESX job and grade')


---- /fixme -----

RegisterCommand('fixme', function()
  local ped = PlayerPedId()
  if DoesEntityExist(ped) and not IsEntityDead(ped) then
    -- kill the player
    SetEntityHealth(ped, 0)

    -- local chat feedback: [FIXME] in bold red, message in white
    TriggerEvent('chat:addMessage', {
      args = { '^1^*FIXME', '^7 You have been fixed.' }
    })
  end
end, false)

---------------