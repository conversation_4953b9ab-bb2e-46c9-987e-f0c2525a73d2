-- server.lua

ESX = nil

-- 1) Retrieve ESX shared object
TriggerEvent('esx:getSharedObject', function(obj)
    ESX = obj
end)

-- 2) Register the /info command
RegisterCommand('info', function(source, args, rawCommand)
    local src = source
    if src == 0 then return end  -- console cannot use

    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local group = xPlayer.getGroup()
    if not (group == 'mod' or group == 'admin' or group == 'cl' or group == 'owner') then
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'You do not have permission to use /info' }
        })
        return
    end

    if #args < 1 then
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Usage: /info <player_id>' }
        })
        return
    end

    local playerId = tonumber(args[1])
    if not playerId then
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Player ID must be a number.' }
        })
        return
    end

    -- 3) Find player by TX player ID and get their identifier
    local targetSource = nil
    if GetResourceState('player-data') == 'started' then
        targetSource = exports['player-data']:getPlayerById(playerId)
    end

    if not targetSource then
        -- Fallback: check if playerId is actually a source ID
        local targetPlayer = ESX.GetPlayerFromId(playerId)
        if targetPlayer then
            targetSource = playerId
        end
    end

    if not targetSource then
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Player with ID ' .. playerId .. ' not found or not online.' }
        })
        return
    end

    local targetPlayer = ESX.GetPlayerFromId(targetSource)
    if not targetPlayer then
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Player data not available.' }
        })
        return
    end

    -- Query the database using identifier
    MySQL.Async.fetchAll([[
        SELECT firstname, lastname, job, job_grade, `group`, identifier
        FROM users
        WHERE identifier = @id
    ]], {
        ['@id'] = targetPlayer.identifier
    }, function(results)
        if results and results[1] then
            local row = results[1]

            -- Build IC name
            local icName   = row.firstname .. ' ' .. row.lastname

            -- Job label / grade
            local jobName       = row.job or 'unemployed'
            local jobGradeIdx   = tonumber(row.job_grade) or 0
            local jobLabel      = 'N/A'
            local jobGradeLabel = tostring(jobGradeIdx)
            if ESX.Jobs[jobName] then
                jobLabel = ESX.Jobs[jobName].label or jobName
                if ESX.Jobs[jobName].grades and ESX.Jobs[jobName].grades[jobGradeIdx] then
                    jobGradeLabel = ESX.Jobs[jobName].grades[jobGradeIdx].label
                end
            end

            -- ESX group
            local esxGroup = row['group'] or 'user'

            -- Prepare payload (omit cfxName)
            local infoData = {
                playerId      = playerId,
                icName        = icName,
                jobLabel      = jobLabel,
                jobGradeLabel = jobGradeLabel,
                esxGroup      = esxGroup
            }

            -- 4) Send to client
            TriggerClientEvent('esx_info:showInfoMenu', src, infoData)
        else
            TriggerClientEvent('chat:addMessage', src, {
                args = { '^1SYSTEM', 'No user found with player_id = ' .. playerId }
            })
        end
    end)
end, false)

-- 5) “Go To” event: teleport runner to target if online
RegisterNetEvent('esx_info:requestGoTo')
AddEventHandler('esx_info:requestGoTo', function(targetPlayerId)
    local src = source

    -- Find target player by TX player ID
    local targetSource = nil
    if GetResourceState('player-data') == 'started' then
        targetSource = exports['player-data']:getPlayerById(targetPlayerId)
    end

    if not targetSource then
        -- Fallback: check if targetPlayerId is actually a source ID
        local targetPlayer = ESX.GetPlayerFromId(targetPlayerId)
        if targetPlayer then
            targetSource = targetPlayerId
        end
    end

    if targetSource then
        local targetPed = GetPlayerPed(targetSource)
        local coords    = GetEntityCoords(targetPed)
        local srcPed    = GetPlayerPed(src)
        SetEntityCoords(srcPed, coords.x, coords.y, coords.z)
        TriggerClientEvent('chat:addMessage', src, {
            args = { '^2SYSTEM', 'Teleported to player ' .. targetSource }
        })
        return
    end

        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Target player is not online.' }
        })
    end)
end)

-- 6) “Bring” event: teleport target to runner if online
RegisterNetEvent('esx_info:requestBring')
AddEventHandler('esx_info:requestBring', function(targetPlayerId)
    local src = source

    -- Find target player by TX player ID
    local targetSource = nil
    if GetResourceState('player-data') == 'started' then
        targetSource = exports['player-data']:getPlayerById(targetPlayerId)
    end

    if not targetSource then
        -- Fallback: check if targetPlayerId is actually a source ID
        local targetPlayer = ESX.GetPlayerFromId(targetPlayerId)
        if targetPlayer then
            targetSource = targetPlayerId
        end
    end

    if targetSource then
        local srcPed    = GetPlayerPed(src)
        local srcCoords = GetEntityCoords(srcPed)
        local targetPed = GetPlayerPed(targetSource)
        SetEntityCoords(targetPed, srcCoords.x, srcCoords.y, srcCoords.z)
        TriggerClientEvent('chat:addMessage', src, {
                    args = { '^2SYSTEM', 'Brought player ' .. pid }
                })
                return
            end
        end

        TriggerClientEvent('chat:addMessage', src, {
            args = { '^1SYSTEM', 'Target player is not online.' }
        })
    end)
end)
