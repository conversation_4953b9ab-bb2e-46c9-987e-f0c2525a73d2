-- Force all job logos to use Sapphire Gaming logo
-- This script ensures that all job images are replaced with sapphire.png

-- Send JavaScript to force all job images to use sapphire.png
local function forceSapphireImage()
    SendNUIMessage({
        action = 'eval',
        data = [[
            // Force all job images to use sapphire.png
            (function() {
                console.log('[OX_INVENTORY] Forcing all job images to use sapphire.png...');

                // Function to replace any job image with sapphire.png
                function forceSapphireImage(img) {
                    if (img.src && img.src.includes('nui://ox_inventory/web/images/') && !img.src.includes('sapphire.png')) {
                        console.log('[OX_INVENTORY] Replacing job image with sapphire.png:', img.src);
                        img.src = 'nui://ox_inventory/web/images/sapphire.png';
                    }
                    // Also handle error cases where job image doesn't exist
                    img.onerror = function() {
                        if (!this.src.includes('sapphire.png')) {
                            console.log('[OX_INVENTORY] Job image failed to load, using sapphire.png:', this.src);
                            this.src = 'nui://ox_inventory/web/images/sapphire.png';
                        }
                    };
                }

                // Set up observer for new images
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) {
                                const images = node.tagName === 'IMG' ? [node] : (node.querySelectorAll ? Array.from(node.querySelectorAll('img')) : []);
                                images.forEach(function(img) {
                                    forceSapphireImage(img);
                                    img.onerror = function() { forceSapphireImage(this); };
                                });
                            }
                        });
                    });
                });

                // Start observing
                if (document.body) {
                    observer.observe(document.body, { childList: true, subtree: true });

                    // Handle existing images
                    Array.from(document.querySelectorAll('img')).forEach(function(img) {
                        forceSapphireImage(img);
                        img.onerror = function() { forceSapphireImage(this); };
                    });
                }

                // Add CSS to force all job images to use sapphire.png as fallback
                const style = document.createElement('style');
                style.textContent = `
                    /* Force all job images to use sapphire.png */
                    img[src*="nui://ox_inventory/web/images/"]:not([src*="weapon_"]):not([src*="ammo"]):not([src*="bandage"]):not([src*="money"]):not([src*="armour"]):not([src*="sapphire.png"]) {
                        content: url('nui://ox_inventory/web/images/sapphire.png') !important;
                    }
                `;
                document.head.appendChild(style);

                // Force specific job images to use sapphire.png
                const jobImages = ['lspd', 'police', 'sheriff', 'ambulance', 'mechanic', 'taxi', 'unemployed'];
                jobImages.forEach(function(job) {
                    const jobImgs = document.querySelectorAll(`img[src*="${job}.png"]`);
                    jobImgs.forEach(function(img) {
                        img.src = 'nui://ox_inventory/web/images/sapphire.png';
                    });
                });

                console.log('[OX_INVENTORY] All job images forced to sapphire.png');
            })();
        ]]
    })
end

-- Initialize when inventory opens
RegisterNetEvent('ox_inventory:openInventory', function()
    Wait(500)
    forceSapphireImage()
end)

-- Initialize when player data is set
AddEventHandler('ox_inventory:setPlayerInventory', function()
    Wait(1000)
    forceSapphireImage()
end)

-- Initialize on resource start
CreateThread(function()
    Wait(3000) -- Wait for UI to be fully loaded
    forceSapphireImage()
end)

-- Periodic check to ensure job images stay as sapphire.png
CreateThread(function()
    while true do
        Wait(5000) -- Check every 5 seconds
        forceSapphireImage()
    end
end)

print("^2[OX_INVENTORY] Sapphire Gaming image force system loaded^7")
