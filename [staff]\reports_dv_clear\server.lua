ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- /report [message]
RegisterCommand('report', function(source, args)
  local msg = table.concat(args, ' ')

  if msg == '' then
    TriggerClientEvent('chat:addMessage', source, {
      color = {255,0,0},
      args  = {'Usage: /report [message]'}
    })
    return
  end

  local xP   = ESX.GetPlayerFromId(source)
  local name = GetPlayerName(source)
  local ident = xP and xP.identifier or ''

  -- Get TX player ID
  local playerId = source -- Default fallback
  if GetResourceState('player-data') == 'started' then
    local txId = exports['player-data']:getPlayerId(source)
    if txId then
      playerId = txId
    end
  end

-- broadcast to staff
for _, pid in ipairs(ESX.GetPlayers()) do
  local px   = ESX.GetPlayerFromId(pid)
  local grp  = px.getGroup and px:getGroup() or px.group
  if grp=='tmod' or grp=='mod' or grp=='admin'
  or grp=='sadmin' or grp=='cl'  or grp=='leadership' then

    local full = string.format(
      '^1^*Report:^r %s (%s)^7 %s',
      name, playerId, msg
    )
    TriggerClientEvent('chat:addMessage', pid, {
      args = { full }
    })
  end
end


-- confirmation to sender
TriggerClientEvent('chat:addMessage', source, {
  args = { '^1^*Report:^r^7 Your report has been sent to online staff—sit tight!' }
})


  -- save to DB
  exports.oxmysql:execute(
    'INSERT INTO reports (identifier, message) VALUES (?, ?)',
    { ident, msg }
  )
end, false)

-- server.lua

ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- /clearall (only admin, cl, or leadership)
RegisterCommand('clearall', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        return
    end

    local group = xPlayer.getGroup()
    if group == 'admin' or group == 'cl' or group == 'leadership' then
        -- Trigger the built-in 'chat:clear' event on every client (-1)
        TriggerClientEvent('chat:clear', -1)
    else
        -- If they aren’t in one of the allowed groups, send a “no permission” message
        TriggerClientEvent('chat:addMessage', source, {
            args = { '^1SYSTEM', 'You do not have permission to use this command.' }
        })
    end
end, false)


-- server.lua

ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- /dv [radius]
RegisterCommand('dv', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local group = xPlayer.getGroup() or 'user'
    if not (group == 'mod' or group == 'admin' or group == 'cl' or group == 'leadership') then
        -- No permission: send a red "no permission" chat message back to the invoker
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 50, 50},
            args  = { '^1SYSTEM', 'You do not have permission to use /dv.' }
        })
        return
    end

    -- Parse the radius argument
    local radius = 5
    if args[1] then
        local num = tonumber(args[1])
        if num and num > 0 then
            radius = math.floor(num)
        end
    end

    -- Trigger the client event on the source player only
    TriggerClientEvent('dv:clientDelete', source, radius)
end, false)
