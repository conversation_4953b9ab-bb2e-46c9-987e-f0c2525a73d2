<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sapphire Gaming Staff Panel</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="staff-panel hidden" id="staffPanel">
        <div class="panel-container">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <div class="logo">
                        <i class="fas fa-gem"></i>
                        <div class="logo-text">
                            <h2>Sapphire Gaming</h2>
                            <span>Staff Panel</span>
                        </div>
                    </div>
                    <div class="welcome-message">
                        <h3 id="welcomeMessage">Welcome back, Staff Member</h3>
                        <p id="currentDate">Loading...</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="search-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search players...">
                        <span class="search-shortcut">CTRL + F</span>
                    </div>
                    <div class="online-count">
                        <i class="fas fa-users"></i>
                        <span>Online Staff (<span id="onlineStaffCount">0</span>)</span>
                    </div>
                    <button class="close-btn" id="closeBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="main-content">
                <!-- Sidebar Navigation -->
                <div class="sidebar">
                    <div class="nav-menu">
                        <div class="nav-item active" data-section="dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </div>
                        <div class="nav-item" data-section="players">
                            <i class="fas fa-users"></i>
                            <span>Players</span>
                        </div>
                        <div class="nav-item" data-section="moderation">
                            <i class="fas fa-shield-alt"></i>
                            <span>Moderation</span>
                        </div>
                        <div class="nav-item admin-only" data-section="server">
                            <i class="fas fa-server"></i>
                            <span>Server</span>
                        </div>
                        <div class="nav-item" data-section="logs">
                            <i class="fas fa-clipboard-list"></i>
                            <span>Logs</span>
                        </div>
                        <div class="nav-item" data-section="reports">
                            <i class="fas fa-flag"></i>
                            <span>Reports</span>
                        </div>
                        <div class="nav-item" data-section="bans">
                            <i class="fas fa-ban"></i>
                            <span>Bans</span>
                        </div>
                        <div class="nav-item" data-section="warnings">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Warnings</span>
                        </div>
                        <div class="nav-item" data-section="vehicles">
                            <i class="fas fa-car"></i>
                            <span>Vehicles</span>
                        </div>
                        <div class="nav-item" data-section="items">
                            <i class="fas fa-box"></i>
                            <span>Items</span>
                        </div>
                    </div>

                    <!-- Staff Info -->
                    <div class="staff-info">
                        <div class="staff-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <div class="staff-details">
                            <h4 id="staffName">Staff Member</h4>
                            <p id="staffRole">Loading...</p>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="content-area">
                    <!-- Dashboard Section -->
                    <div class="section active" id="dashboard-section">
                        <div class="section-header">
                            <h3>Dashboard Overview</h3>
                            <button class="refresh-btn" id="refreshBtn">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalPlayers">0</h4>
                                    <p>Online Players</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalStaff">0</h4>
                                    <p>Staff Online</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="serverUptime">00:00:00</h4>
                                    <p>Server Uptime</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="recentReports">0</h4>
                                    <p>Recent Reports</p>
                                </div>
                            </div>
                        </div>

                        <div class="recent-activity">
                            <h4>Recent Activity</h4>
                            <div class="activity-list" id="activityList">
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="activity-content">
                                        <p>No recent activity</p>
                                        <span class="activity-time">Just now</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Players Section -->
                    <div class="section" id="players-section">
                        <div class="section-header">
                            <h3>Online Players</h3>
                            <div class="section-actions">
                                <button class="action-btn">
                                    <i class="fas fa-download"></i>
                                    Export List
                                </button>
                            </div>
                        </div>

                        <div class="players-list" id="playersList">
                            <!-- Players will be populated here -->
                        </div>
                    </div>

                    <!-- Moderation Section -->
                    <div class="section" id="moderation-section">
                        <div class="section-header">
                            <h3>Moderation Tools</h3>
                        </div>

                        <div class="moderation-tools">
                            <div class="tool-card">
                                <div class="tool-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Names Display</h4>
                                    <p>Toggle player names and health bars visibility</p>
                                    <button class="action-btn" id="toggleNamesBtn">
                                        <i class="fas fa-toggle-off"></i>
                                        <span id="namesButtonText">Enable Names</span>
                                    </button>
                                </div>
                            </div>

                            <div class="tool-card">
                                <div class="tool-icon">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Quick Actions</h4>
                                    <p>Access to kick, ban, and teleport functions</p>
                                    <button class="action-btn" disabled>
                                        <i class="fas fa-info-circle"></i>
                                        Available in Players Tab
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section admin-only" id="server-section">
                        <div class="section-header">
                            <h3>Server Management</h3>
                            <p class="admin-only-notice">Admin+ Only Features</p>
                        </div>

                        <div class="server-tools">
                            <div class="tool-card admin-only">
                                <div class="tool-icon">
                                    <i class="fas fa-skull-crossbones"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Kill All Players</h4>
                                    <p>Instantly kill all players on the server</p>
                                    <button class="action-btn danger" id="killAllBtn">
                                        <i class="fas fa-skull-crossbones"></i>
                                        Kill All
                                    </button>
                                </div>
                            </div>

                            <div class="tool-card admin-only">
                                <div class="tool-icon">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Heal All Players</h4>
                                    <p>Heal all players to full health (revives dead, heals injured)</p>
                                    <button class="action-btn" id="reviveAllBtn">
                                        <i class="fas fa-heart"></i>
                                        Heal All
                                    </button>
                                </div>
                            </div>

                            <div class="tool-card admin-only">
                                <div class="tool-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Change Time</h4>
                                    <p>Set the server time</p>
                                    <div class="time-controls">
                                        <input type="number" id="timeHour" min="0" max="23" placeholder="Hour (0-23)">
                                        <input type="number" id="timeMinute" min="0" max="59" placeholder="Minute (0-59)">
                                        <button class="action-btn" id="setTimeBtn">
                                            <i class="fas fa-clock"></i>
                                            Set Time
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="tool-card admin-only">
                                <div class="tool-icon">
                                    <i class="fas fa-cloud-sun"></i>
                                </div>
                                <div class="tool-info">
                                    <h4>Change Weather</h4>
                                    <p>Set the server weather</p>
                                    <div class="weather-controls">
                                        <select id="weatherSelect">
                                            <option value="CLEAR">Clear</option>
                                            <option value="EXTRASUNNY">Extra Sunny</option>
                                            <option value="CLOUDS">Cloudy</option>
                                            <option value="OVERCAST">Overcast</option>
                                            <option value="RAIN">Rain</option>
                                            <option value="CLEARING">Clearing</option>
                                            <option value="THUNDER">Thunder</option>
                                            <option value="SMOG">Smog</option>
                                            <option value="FOGGY">Foggy</option>
                                            <option value="XMAS">Christmas</option>
                                            <option value="SNOWLIGHT">Light Snow</option>
                                            <option value="BLIZZARD">Blizzard</option>
                                        </select>
                                        <button class="action-btn" id="setWeatherBtn">
                                            <i class="fas fa-cloud-sun"></i>
                                            Set Weather
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="section" id="logs-section">
                        <div class="section-header">
                            <h3>Staff Action Logs</h3>
                            <div class="section-actions">
                                <button class="action-btn" id="clearLogsBtn">
                                    <i class="fas fa-trash"></i>
                                    Clear Logs
                                </button>
                            </div>
                        </div>

                        <div class="logs-container">
                            <div class="logs-filters">
                                <select id="logTypeFilter">
                                    <option value="all">All Actions</option>
                                    <option value="NAMES">Names</option>
                                    <option value="KICK">Kicks</option>
                                    <option value="BAN">Bans</option>
                                    <option value="TELEPORT">Teleports</option>
                                    <option value="BRING">Brings</option>
                                    <option value="KILL_ALL">Kill All</option>
                                    <option value="REVIVE_ALL">Revive All</option>
                                    <option value="TIME_CHANGE">Time Changes</option>
                                    <option value="WEATHER_CHANGE">Weather Changes</option>
                                    <option value="CLEAR_INV">Clear Inventory</option>
                                </select>
                            </div>

                            <div class="logs-list" id="logsList">
                                <div class="log-item">
                                    <div class="log-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="log-content">
                                        <p>No logs available</p>
                                        <span class="log-time">Waiting for data...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reports Section -->
                    <div class="section" id="reports-section">
                        <div class="section-header">
                            <h3>Player Reports</h3>
                            <div class="section-actions">
                                <button class="action-btn" id="refreshReportsBtn">
                                    <i class="fas fa-sync-alt"></i>
                                    Refresh Reports
                                </button>
                            </div>
                        </div>

                        <div class="reports-container">
                            <div class="reports-filters">
                                <select id="reportStatusFilter">
                                    <option value="all">All Reports</option>
                                    <option value="open">Open</option>
                                    <option value="claimed">Claimed</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>

                            <div class="reports-list" id="reportsList">
                                <div class="report-item">
                                    <div class="report-header">
                                        <h5>No reports available</h5>
                                        <span class="report-status open">Open</span>
                                    </div>
                                    <p>No reports to display</p>
                                    <div class="report-footer">
                                        <span>Reporter: System</span>
                                        <span>Created: Just now</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bans Section -->
                    <div class="section" id="bans-section">
                        <div class="section-header">
                            <h3>Ban Management</h3>
                            <div class="section-actions">
                                <button class="action-btn" id="refreshBansBtn">
                                    <i class="fas fa-sync-alt"></i>
                                    Refresh Bans
                                </button>
                            </div>
                        </div>

                        <div class="bans-container">
                            <div class="bans-search">
                                <input type="text" id="banSearchInput" placeholder="Search bans by name, ID, or reason...">
                            </div>

                            <div class="bans-list" id="bansList">
                                <div class="ban-item">
                                    <div class="ban-header">
                                        <h5>No bans found</h5>
                                        <span class="ban-status active">Active</span>
                                    </div>
                                    <div class="ban-details">
                                        <p><strong>Reason:</strong> No bans to display</p>
                                        <p><strong>Banned By:</strong> System</p>
                                        <p><strong>Duration:</strong> N/A</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Warnings Section -->
                    <div class="section" id="warnings-section">
                        <div class="section-header">
                            <h3>Warning Management</h3>
                            <div class="section-actions">
                                <button class="action-btn" id="refreshWarningsBtn">
                                    <i class="fas fa-sync-alt"></i>
                                    Refresh Warnings
                                </button>
                            </div>
                        </div>

                        <div class="warnings-container">
                            <div class="warnings-search">
                                <input type="text" id="warningSearchInput" placeholder="Search warnings by player name...">
                            </div>

                            <div class="warnings-list" id="warningsList">
                                <div class="warning-item">
                                    <div class="warning-header">
                                        <h5>No warnings found</h5>
                                        <span class="warning-date">Just now</span>
                                    </div>
                                    <div class="warning-details">
                                        <p><strong>Reason:</strong> No warnings to display</p>
                                        <p><strong>Warned By:</strong> System</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Vehicles Section -->
                    <div class="section" id="vehicles-section">
                        <div class="section-header">
                            <h3>Vehicle Management</h3>
                            <div class="section-actions">
                                <button class="action-btn" id="deleteAllVehiclesBtn">
                                    <i class="fas fa-trash"></i>
                                    Delete All Vehicles
                                </button>
                            </div>
                        </div>

                        <div class="vehicles-container">
                            <div class="vehicle-spawn">
                                <h4>Spawn Vehicle</h4>
                                <div class="spawn-controls">
                                    <select id="vehicleCategorySelect">
                                        <option value="">Select Category</option>
                                        <option value="compacts">Compacts</option>
                                        <option value="sedans">Sedans</option>
                                        <option value="suvs">SUVs</option>
                                        <option value="coupes">Coupes</option>
                                        <option value="muscle">Muscle</option>
                                        <option value="sportsclassics">Sports Classics</option>
                                        <option value="sports">Sports</option>
                                        <option value="super">Super</option>
                                        <option value="motorcycles">Motorcycles</option>
                                        <option value="offroad">Off-Road</option>
                                        <option value="emergency">Emergency</option>
                                        <option value="helicopters">Helicopters</option>
                                        <option value="planes">Planes</option>
                                    </select>
                                    <input type="text" id="vehicleModelInput" placeholder="Vehicle model (e.g., adder, zentorno)">
                                    <select id="spawnTargetSelect">
                                        <option value="self">Spawn for Self</option>
                                    </select>
                                    <button class="action-btn" id="spawnVehicleBtn">
                                        <i class="fas fa-car"></i>
                                        Spawn Vehicle
                                    </button>
                                </div>
                            </div>

                            <div class="vehicle-list" id="vehicleList">
                                <h4>Recent Vehicle Spawns</h4>
                                <div class="vehicle-logs">
                                    <p>No recent vehicle spawns</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Items Section -->
                    <div class="section" id="items-section">
                        <div class="section-header">
                            <h3>Item Management</h3>
                        </div>

                        <div class="items-container">
                            <div class="item-give">
                                <h4>Give Item</h4>
                                <div class="give-controls">
                                    <button class="action-btn" id="loadItemsBtn" style="margin-bottom: 10px;">
                                        <i class="fas fa-sync-alt"></i>
                                        Load Items
                                    </button>
                                    <div class="item-search-container">
                                        <input type="text" id="itemSearchInput" placeholder="Search items..." autocomplete="off">
                                        <div class="item-dropdown" id="itemDropdown">
                                            <div class="dropdown-item" data-item="bread">
                                                <span class="item-name">Bread</span>
                                                <span class="item-weight">125g</span>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="number" id="itemQuantityInput" placeholder="Quantity" min="1" max="1000" value="1">
                                    <select id="itemTargetSelect">
                                        <option value="">Select Player</option>
                                    </select>
                                    <button class="action-btn" id="giveItemBtn">
                                        <i class="fas fa-gift"></i>
                                        Give Item
                                    </button>
                                </div>
                            </div>

                            <div class="item-list" id="itemList">
                                <h4>Recent Item Transactions</h4>
                                <div class="item-logs">
                                    <p>No recent item transactions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Player Info Section -->
                    <div class="section hidden" id="player-info-section">
                        <div class="section-header">
                            <h3>Player Information</h3>
                            <button class="refresh-btn" id="backToPlayersBtn">
                                <i class="fas fa-arrow-left"></i>
                                Back to Players
                            </button>
                        </div>

                        <div class="player-info-content">
                            <div class="player-info-header">
                                <div class="player-info-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="player-info-basic">
                                    <h4 id="playerInfoName">Player Name</h4>
                                    <p id="playerInfoId">ID: 0</p>
                                </div>
                            </div>

                            <div class="player-info-details">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <label>Job:</label>
                                        <span id="playerInfoJob">unemployed</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Job Grade:</label>
                                        <span id="playerInfoJobGrade">0</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Group:</label>
                                        <span id="playerInfoGroup">user</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Money:</label>
                                        <span id="playerInfoMoney">$0</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Bank:</label>
                                        <span id="playerInfoBank">$0</span>
                                    </div>
                                    <div class="info-item">
                                        <label>Identifier:</label>
                                        <span id="playerInfoIdentifier">steam:000000</span>
                                    </div>
                                </div>
                            </div>

                            <div class="player-info-actions">
                                <div class="action-group">
                                    <h5>Teleport Actions</h5>
                                    <button class="action-btn" onclick="teleportToPlayerFromInfo()">
                                        <i class="fas fa-location-arrow"></i> Goto Player
                                    </button>
                                    <button class="action-btn" onclick="bringPlayerFromInfo()">
                                        <i class="fas fa-hand-paper"></i> Bring Player
                                    </button>
                                </div>

                                <div class="action-group">
                                    <h5>Moderation Actions</h5>
                                    <button class="action-btn danger" onclick="kickPlayerFromInfo()">
                                        <i class="fas fa-sign-out-alt"></i> Kick Player
                                    </button>
                                    <button class="action-btn danger" onclick="banPlayerFromInfo()">
                                        <i class="fas fa-ban"></i> Ban Player
                                    </button>
                                    <button class="action-btn warning" onclick="warnPlayerFromInfo()">
                                        <i class="fas fa-exclamation-triangle"></i> Warn Player
                                    </button>
                                    <button class="action-btn warning" onclick="clearPlayerInventory()">
                                        <i class="fas fa-trash"></i> Clear Inventory
                                    </button>
                                </div>

                                <div class="action-group">
                                    <h5>Advanced Actions</h5>
                                    <button class="action-btn" onclick="spectatePlayerFromInfo()">
                                        <i class="fas fa-eye"></i> Spectate Player
                                    </button>
                                    <button class="action-btn" onclick="screenshotPlayerFromInfo()">
                                        <i class="fas fa-camera"></i> Take Screenshot
                                    </button>
                                    <button class="action-btn" onclick="freezePlayerFromInfo()">
                                        <i class="fas fa-snowflake"></i> Freeze Player
                                    </button>
                                    <button class="action-btn" onclick="revivePlayerFromInfo()">
                                        <i class="fas fa-heart"></i> Revive Player
                                    </button>
                                </div>

                                <div class="action-group">
                                    <h5>Money Management</h5>
                                    <div class="money-controls">
                                        <select id="moneyAccountType">
                                            <option value="money">Cash</option>
                                            <option value="bank">Bank</option>
                                            <option value="black_money">Black Money</option>
                                        </select>
                                        <input type="number" id="moneyAmount" placeholder="Amount" min="1">
                                        <button class="action-btn" onclick="giveMoneyFromInfo()">
                                            <i class="fas fa-plus"></i> Give
                                        </button>
                                        <button class="action-btn warning" onclick="takeMoneyFromInfo()">
                                            <i class="fas fa-minus"></i> Take
                                        </button>
                                    </div>
                                </div>

                                <div class="action-group">
                                    <h5>Player Information</h5>
                                    <button class="action-btn" onclick="viewPlayerWarnings()">
                                        <i class="fas fa-list"></i> View Warnings
                                    </button>
                                    <button class="action-btn" onclick="viewPlayerNotes()">
                                        <i class="fas fa-sticky-note"></i> View Notes
                                    </button>
                                    <button class="action-btn" onclick="addPlayerNote()">
                                        <i class="fas fa-plus"></i> Add Note
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Online Staff Sidebar -->
                <div class="online-staff-sidebar">
                    <h4>Online Staff</h4>
                    <div class="staff-list" id="onlineStaffList">
                        <!-- Online staff will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
