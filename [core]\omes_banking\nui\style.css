* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    overflow: hidden;
    user-select: none;
}

.banking-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1200px;
    height: 800px;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.banking-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    padding: 18px 30px;
    border-bottom: 1px solid #2d2d2d;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.banking-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.03) 50%, transparent 100%);
    pointer-events: none;
}

.header-left,
.header-center,
.header-right {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-left {
    flex: 1;
}

.header-center {
    flex: 0 0 auto;
}

.header-right {
    flex: 1;
    justify-content: flex-end;
}

.bank-logo {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: #22c55e;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.logo-icon i {
    font-size: 18px;
    color: #ffffff;
}

.bank-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.bank-title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.bank-subtitle {
    color: #94a3b8;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.7;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-name {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
    line-height: 1.2;
    background: rgba(15, 15, 15, 0.6);
    border: 1px solid #2d2d2d;
    border-radius: 22px;
    padding: 8px 16px;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar {
    width: 24px;
    height: 24px;
    background: #ffffff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-avatar i {
    font-size: 12px;
    color: #1a1a1a;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.current-time {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-right: 16px;
    background: rgba(15, 15, 15, 0.6);
    border: 1px solid #2d2d2d;
    border-radius: 22px;
    padding: 8px 14px;
    backdrop-filter: blur(10px);
}

.close-btn {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 1px solid #2d2d2d;
    transition: all 0.2s ease;
    background: #2d2d2d;
    color: #94a3b8;
}

.close-btn:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: #ffffff;
}

.banking-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.tab-navigation {
    background: #0f0f0f;
    padding: 8px 20px;
    margin-top: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.tab-items {
    display: flex;
    gap: 4px;
    background: #1a1a1a;
    border-radius: 12px;
    padding: 6px;
    border: 1px solid #2d2d2d;
}

.tab-item {
    padding: 12px 20px;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    background: transparent;
    font-size: 13px;
    font-weight: 500;
    border-radius: 8px;
    position: relative;
    min-width: 100px;
    justify-content: center;
    text-transform: capitalize;
    letter-spacing: 0.3px;
}

.tab-item:hover {
    color: #ffffff;
    background: rgba(45, 45, 45, 0.6);
}

.tab-item.active {
    color: #ffffff;
    background: #22c55e;
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.tab-item i {
    font-size: 13px;
    opacity: 0.9;
}

.tab-item.active i {
    opacity: 1;
}

.sidebar {
    display: none;
}

.main-content {
    flex: 1;
    padding: 40px;
    background: #0f0f0f;
    overflow-y: auto;
}

.account-overview {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
}

.balance-card {
    text-align: center;
    margin-bottom: 20px;
}

.balance-label {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 8px;
}

.balance-amount {
    color: #22c55e;
    font-size: 36px;
    font-weight: 700;
}

.credit-card {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.credit-card:last-child {
    margin-bottom: 0;
}

.overview-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.left-section {
    flex: 0 0 450px;
    display: flex;
    flex-direction: column;
}

.right-section {
    flex: 1;
}

.card-background {
    width: 400px;
    height: 250px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 1px solid #404040;
    border-radius: 16px;
    padding: 25px;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.card-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(34, 197, 94, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.bank-name {
    color: #22c55e;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.card-type {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    background: rgba(148, 163, 184, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #404040;
}

.card-number {
    margin-bottom: 40px;
    text-align: center;
}

.card-number span {
    color: #ffffff;
    font-size: 24px;
    font-weight: 500;
    letter-spacing: 3px;
    font-family: 'Courier New', monospace;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.card-holder, .card-balance {
    display: flex;
    flex-direction: column;
}

.card-holder .label, .card-balance .label {
    color: #94a3b8;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.card-holder .name {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
}

.card-balance .amount {
    color: #22c55e;
    font-size: 30px;
    font-weight: 700;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
}

.action-btn {
    background: #22c55e;
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn:hover {
    background: #16a34a;
}

.action-btn.secondary {
    background: #475569;
}

.action-btn.secondary:hover {
    background: #64748b;
}

.transaction-form {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    padding: 25px;
}

.transfer-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.transfer-form-section {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 30px;
}

.transfer-preview-section {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.transfer-preview-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 1px solid #404040;
    border-radius: 12px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.transfer-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(34, 197, 94, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.preview-icon {
    width: 40px;
    height: 40px;
    background: #22c55e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.preview-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.preview-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.preview-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #2d2d2d;
}

.preview-row:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 16px;
}

.preview-label {
    color: #94a3b8;
    font-size: 14px;
    font-weight: 500;
}

.preview-value {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.preview-amount {
    color: #22c55e;
    font-size: 18px;
    font-weight: 700;
}

.form-group-enhanced {
    margin-bottom: 25px;
    position: relative;
}

.form-label-enhanced {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-input-enhanced {
    width: 100%;
    background: #0f0f0f;
    border: 2px solid #2d2d2d;
    border-radius: 8px;
    padding: 16px 20px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-input-enhanced:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    transform: translateY(-1px);
}

.form-input-enhanced::placeholder {
    color: #64748b;
    font-weight: 400;
}

.transfer-action-btn {
    width: 100%;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 10px;
}

.transfer-action-btn:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.transfer-action-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.account-balance-card {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
}

.balance-label-small {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.balance-amount-small {
    color: #22c55e;
    font-size: 24px;
    font-weight: 700;
}

.deposit-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.deposit-form-section {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 30px;
}

.deposit-info-section {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.withdraw-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.withdraw-form-section {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 30px;
}

.withdraw-info-section {
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.withdraw-preview-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 1px solid #404040;
    border-radius: 12px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.withdraw-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(239, 68, 68, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.withdraw-action-btn {
    width: 100%;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 10px;
}

.withdraw-action-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.withdraw-action-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.withdraw-preview-icon {
    width: 40px;
    height: 40px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.withdraw-amount {
    color: #ef4444;
    font-size: 18px;
    font-weight: 700;
}

.history-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.history-main-section {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 30px;
    max-height: 580px;
    overflow: hidden;
}

.history-sidebar {
    flex: 0 0 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.history-filters-card {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 15px;
}

.history-stats-card {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
}

.filter-group {
    margin-bottom: 12px;
}

.filter-label {
    color: #ffffff;
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    width: 100%;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 6px;
    padding: 8px 10px;
    color: #ffffff;
    font-size: 13px;
    transition: border-color 0.2s;
}

.filter-select:focus {
    outline: none;
    border-color: #22c55e;
}

.filter-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.filter-btn {
    background: #2d2d2d;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.filter-btn:hover {
    background: #22c55e;
    border-color: #22c55e;
}

.filter-btn.active {
    background: #22c55e;
    border-color: #22c55e;
}

.stats-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.stats-summary .summary-item {
    text-align: center;
    padding: 8px;
    background: none;
    border: none;
    border-radius: 0;
    display: block;
}

.stats-summary .summary-label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: block;
    gap: 0;
}

.stats-summary .summary-value {
    color: #ffffff;
    font-size: 18px;
    font-weight: 700;
}

.stats-summary .summary-value.positive {
    color: #22c55e;
}

.stats-summary .summary-value.negative {
    color: #ef4444;
}

.transaction-list-enhanced {
    max-height: 420px;
    overflow-y: auto;
    margin-top: 20px;
    padding: 0 15px 10px 15px;
}

.transaction-item-enhanced {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #2d2d2d;
    transition: background 0.2s ease;
}

.transaction-item-enhanced:hover {
    background: rgba(45, 45, 45, 0.3);
    margin: 0 -15px;
    padding: 16px 15px;
    border-radius: 8px;
}

.transaction-item-enhanced:last-child {
    border-bottom: none;
}

.transaction-info-enhanced {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.transaction-icon-enhanced {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.transaction-icon-enhanced.deposit {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.transaction-icon-enhanced.withdrawal {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.transaction-icon-enhanced.transfer_in {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.transaction-icon-enhanced.transfer_out {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
}

.transaction-icon-enhanced.fee {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.transaction-icon-enhanced.savings_deposit {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.transaction-icon-enhanced.savings_withdrawal {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.transaction-icon-enhanced.savings_opened {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
}

.transaction-icon-enhanced.savings_closed {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.transaction-icon-enhanced.account_transfer {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
}

.transaction-details-enhanced {
    flex: 1;
}

.transaction-details-enhanced h4 {
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 4px;
}

.transaction-details-enhanced p {
    color: #94a3b8;
    font-size: 12px;
    margin: 0;
}

.transaction-amount-enhanced {
    text-align: right;
    flex-shrink: 0;
}

.transaction-amount-enhanced .amount {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 2px;
}

.transaction-amount-enhanced .amount.positive {
    color: #22c55e;
}

.transaction-amount-enhanced .amount.negative {
    color: #ef4444;
}

.transaction-amount-enhanced .time {
    color: #94a3b8;
    font-size: 11px;
    font-weight: 500;
}

.search-box {
    width: 100%;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 14px;
    transition: border-color 0.2s;
    margin-bottom: 20px;
}

.search-box:focus {
    outline: none;
    border-color: #22c55e;
}

.search-box::placeholder {
    color: #64748b;
}

.no-transactions-enhanced {
    text-align: center;
    padding: 60px 20px;
    color: #94a3b8;
}

.no-transactions-enhanced i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-transactions-enhanced h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #ffffff;
}

.no-transactions-enhanced p {
    font-size: 14px;
    line-height: 1.5;
}

.deposit-preview-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 1px solid #404040;
    border-radius: 12px;
    padding: 25px;
    position: relative;
    overflow: hidden;
}

.deposit-preview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(34, 197, 94, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.deposit-action-btn {
    width: 100%;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 10px;
}

.deposit-action-btn:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
}

.deposit-action-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.amount-suggestions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.suggestion-btn {
    background: #2d2d2d;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
}

.suggestion-btn:hover {
    background: #22c55e;
    border-color: #22c55e;
    transform: translateY(-1px);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.form-input {
    width: 100%;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 6px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #22c55e;
}

.form-input::placeholder {
    color: #64748b;
}

.transaction-history {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    max-height: 400px;
    overflow-y: auto;
}

.overview-layout .transaction-history {
    max-height: 280px;
    margin-bottom: 0;
}

.overview-layout .transaction-history .section-title {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.section-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #2d2d2d;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    background: #2d2d2d;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #22c55e;
}

.transaction-details h4 {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.transaction-details p {
    color: #94a3b8;
    font-size: 12px;
}

.transaction-amount {
    color: #22c55e;
    font-weight: 600;
}

.transaction-amount.negative {
    color: #ef4444;
}

.hidden {
    display: none !important;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #2d2d2d;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #404040;
}

.card-background.savings-card {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #1a1a2e 100%);
    border: 1px solid #3b4f7d;
}

.card-background.savings-card::before {
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
}

.card-background.savings-card .bank-name {
    color: #3b82f6;
}

.card-background.savings-card .card-balance .amount {
    color: #3b82f6;
}

.card-background.savings-card.blurred {
    position: relative;
    transition: all 0.3s ease;
}

.card-background.savings-card.blurred > *:not(.savings-overlay) {
    filter: blur(4px);
    opacity: 0.7;
}

.savings-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 16px;
    transition: background 0.3s ease;
    z-index: 10;
}

.savings-overlay:hover {
    background: rgba(0, 0, 0, 0.7);
}

.overlay-content {
    text-align: center;
    color: #ffffff;
}

.plus-icon {
    width: 60px;
    height: 60px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.savings-overlay:hover .plus-icon {
    transform: scale(1.1);
    background: #2563eb;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.overlay-text {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-chart-container {
    padding: 8px 0 0 20px;
    margin-bottom: 10px;
}

.balance-chart-container .section-title {
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.chart-title-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.chart-title-container .section-title {
    margin-bottom: 0;
}

.time-period-tag {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
    font-size: 11px;
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    cursor: default;
}

.time-period-tag:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.5);
}

.chart-wrapper {
    height: 220px;
    margin-top: 20px;
    position: relative;
    background: rgba(15, 15, 15, 0.5);
    border-radius: 8px;
    padding: 15px;
}

/* Modern Savings Page Styles */
.savings-layout {
    padding: 0;
}

.savings-modern-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.savings-left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.savings-right-section {
    flex: 0 0 300px;
}

.savings-balances {
    display: flex;
    gap: 20px;
}

.balance-card-modern {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.balance-card-modern.savings {
    border-color: #3b4f7d;
}

.balance-icon {
    width: 50px;
    height: 50px;
    background: #2d2d2d;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #22c55e;
    font-size: 20px;
}

.balance-card-modern.savings .balance-icon {
    background: #3b4f7d;
    color: #3b82f6;
}

.balance-info {
    flex: 1;
}

.balance-label {
    color: #94a3b8;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.balance-amount {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
}

.transfer-modern-card {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 25px;
}

.card-header-modern {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    color: #22c55e;
    font-size: 16px;
    font-weight: 600;
}

.card-header-modern i {
    font-size: 18px;
}

.transfer-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.direction-selector {
    display: flex;
    gap: 10px;
}

.direction-btn {
    flex: 1;
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 15px;
    color: #94a3b8;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
}

.direction-btn:hover {
    border-color: #22c55e;
    color: #ffffff;
}

.direction-btn.active {
    background: rgba(34, 197, 94, 0.1);
    border-color: #22c55e;
    color: #22c55e;
}

.transfer-amount-input {
    width: 100%;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    padding: 15px;
    color: #ffffff;
    font-size: 16px;
    text-align: center;
}

.transfer-amount-input:focus {
    outline: none;
    border-color: #22c55e;
}

.quick-amounts-modern {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-amount-modern {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
}

.quick-amount-modern:hover:not(:disabled) {
    background: #22c55e;
    border-color: #22c55e;
}

.quick-amount-modern:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.transfer-execute-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    border-radius: 8px;
    padding: 15px;
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.transfer-execute-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.transfer-execute-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.error-message {
    color: #ef4444;
    font-size: 13px;
    text-align: center;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    padding: 8px 12px;
}

.quick-actions-modern {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
}

.quick-action-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    color: #22c55e;
    font-size: 14px;
    font-weight: 600;
}

.action-buttons-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-btn-modern {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 15px 12px;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    text-align: left;
}

.action-btn-modern:hover:not(:disabled) {
    border-color: #22c55e;
}

.action-btn-modern.deposit:hover:not(:disabled) {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

.action-btn-modern.withdraw:hover:not(:disabled) {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.action-btn-modern.info:hover:not(:disabled) {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.action-btn-modern.close-account:hover:not(:disabled) {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.action-btn-modern:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn-modern i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.action-btn-modern span {
    font-weight: 600;
    font-size: 13px;
}

.action-btn-modern small {
    color: #94a3b8;
    font-size: 11px;
    margin-top: 2px;
}

.action-btn-modern div {
    display: flex;
    flex-direction: column;
}

/* Quick Action Modal Styles */
.transfer-info {
    margin-bottom: 20px;
}

.transfer-route {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 16px;
}

.from-account, .to-account {
    color: #22c55e;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.transfer-route i {
    color: #94a3b8;
}

.available-balance {
    text-align: center;
    color: #94a3b8;
    font-size: 14px;
}

.amount-input-section {
    margin-bottom: 20px;
}

.amount-input-section label {
    display: block;
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.quick-action-input {
    width: 100%;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
    padding: 15px;
    color: #ffffff;
    font-size: 18px;
    text-align: center;
    font-weight: 600;
}

.quick-action-input:focus {
    outline: none;
    border-color: #22c55e;
}

.quick-amounts {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.quick-amount-btn {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 8px 16px;
    color: #ffffff;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
}

.quick-amount-btn:hover:not(:disabled) {
    background: #22c55e;
    border-color: #22c55e;
}

.quick-amount-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Summary Grid Styles */
.summary-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 8px;
}

.summary-item.total {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

.summary-label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #94a3b8;
    font-weight: 600;
    font-size: 14px;
}

.summary-label i {
    width: 16px;
    text-align: center;
}

/* Fix modal title icon spacing */
.confirm-modal-title i {
    margin-right: 8px;
}

/* Quick Action Modal Header Colors */
.quick-action-modal-savings .confirm-modal-header {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.quick-action-modal-checking .confirm-modal-header {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.quick-action-modal-summary .confirm-modal-header {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Quick Action Modal Button Colors */
.quick-action-modal-savings .confirm-delete-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.quick-action-modal-savings .confirm-delete-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.summary-value {
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
}

.summary-value.checking {
    color: #ffffff;
}

.summary-value.savings {
    color: #ffffff;
}

.summary-value.total-value {
    color: #22c55e;
    font-size: 18px;
}

/* Responsive Design for Modern Savings */
@media (max-width: 1024px) {
    .savings-modern-layout {
        flex-direction: column;
    }
    
    .savings-right-section {
        flex: none;
        width: 100%;
    }
    
    .savings-balances {
        flex-direction: column;
    }
    
    .quick-amounts {
        justify-content: center;
    }
}



.chart-wrapper canvas {
    max-height: 100%;
    width: 100% !important;
    height: 100% !important;
    border-radius: 6px;
}

.no-transactions {
    text-align: center;
    padding: 40px 20px;
    color: #94a3b8;
    font-style: italic;
}

.no-transactions-overview {
    text-align: center;
    padding: 40px 20px;
    color: #94a3b8;
}

.no-transactions-overview i {
    font-size: 36px;
    margin-bottom: 12px;
    opacity: 0.5;
}

.no-transactions-overview h3 {
    font-size: 16px;
    margin-bottom: 6px;
    color: #ffffff;
}

.no-transactions-overview p {
    font-size: 13px;
    line-height: 1.4;
}

/* Card Type Section with PIN Button */
.card-type-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pin-setup-btn {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22c55e;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.pin-setup-btn:hover {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.5);
    transform: translateY(-1px);
}

.pin-setup-btn i {
    font-size: 11px;
}

/* PIN Modal Styles */
.pin-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.pin-modal {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 16px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
    overflow: hidden;
}

.pin-modal-header {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pin-modal-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.pin-modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

.pin-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.pin-modal-content {
    padding: 30px 25px;
}

.pin-display {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.pin-digit {
    width: 50px;
    height: 50px;
    border: 2px solid #2d2d2d;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #22c55e;
    font-weight: 700;
    background: #0f0f0f;
    transition: all 0.3s ease;
}

.pin-digit.filled {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
}

.pin-description {
    text-align: center;
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 30px;
    line-height: 1.5;
}

.pin-error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 20px;
    color: #ef4444;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.3s ease-out;
}

.pin-error-message i {
    font-size: 14px;
    flex-shrink: 0;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.number-pad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 30px;
}

.number-btn {
    width: 100%;
    height: 60px;
    background: #2d2d2d;
    border: 1px solid #404040;
    border-radius: 12px;
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.number-btn:hover:not(:disabled):not(.empty) {
    background: #22c55e;
    border-color: #22c55e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.number-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.number-btn.empty {
    background: transparent;
    border: none;
    cursor: default;
}

.number-btn.backspace-btn {
    background: #ef4444;
    border-color: #ef4444;
}

.number-btn.backspace-btn:hover:not(:disabled) {
    background: #dc2626;
    border-color: #dc2626;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.pin-modal-actions {
    display: flex;
    gap: 15px;
}

.pin-cancel-btn {
    flex: 1;
    background: transparent;
    border: 1px solid #2d2d2d;
    color: #94a3b8;
    padding: 14px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.pin-cancel-btn:hover {
    background: #2d2d2d;
    color: #ffffff;
}

.pin-next-btn {
    flex: 1;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    color: #ffffff;
    padding: 14px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.pin-next-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.pin-next-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* PIN Display Section */
.pin-display-section {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 4px;
    padding: 4px 6px;
    vertical-align: middle;
}

.pin-text {
    color: #22c55e;
    font-size: 12px;
    font-weight: 600;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    min-width: 28px;
    text-align: center;
    display: inline-block;
    line-height: 1;
}

.pin-toggle-btn {
    background: transparent;
    border: none;
    color: #22c55e;
    cursor: pointer;
    padding: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    width: 16px;
    height: 16px;
    line-height: 1;
}

.pin-toggle-btn:hover {
    background: rgba(34, 197, 94, 0.2);
}

.pin-toggle-btn i {
    font-size: 10px;
}

.pin-edit-btn {
    background: transparent;
    border: none;
    color: #22c55e;
    cursor: pointer;
    padding: 2px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    width: 16px;
    height: 16px;
    line-height: 1;
}

.pin-edit-btn:hover {
    background: rgba(34, 197, 94, 0.2);
}

.pin-edit-btn i {
    font-size: 10px;
}

/* Clear Confirmation Modal */
.confirm-modal {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 16px;
    width: 500px;
    max-width: 90vw;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
    overflow: hidden;
}

.confirm-modal-header {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.confirm-modal-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.confirm-modal-content {
    padding: 30px 25px;
    text-align: center;
}

.confirm-icon {
    width: 80px;
    height: 80px;
    background: rgba(239, 68, 68, 0.1);
    border: 2px solid rgba(239, 68, 68, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.confirm-icon i {
    font-size: 32px;
    color: #ef4444;
}

.confirm-message h4 {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.confirm-message p {
    color: #94a3b8;
    font-size: 14px;
    line-height: 1.6;
    margin: 0 0 30px 0;
}

.confirm-modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.confirm-cancel-btn {
    background: transparent;
    border: 1px solid #2d2d2d;
    color: #94a3b8;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    min-width: 120px;
}

.confirm-cancel-btn:hover {
    background: #2d2d2d;
    color: #ffffff;
}

.confirm-delete-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.confirm-delete-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.confirm-delete-btn i {
    font-size: 12px;
}

/* ATM Mode Styles */
.atm-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.atm-card-section {
    flex: 0 0 450px;
    display: flex;
    justify-content: center;
}

.atm-info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.atm-welcome {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
}

.atm-welcome h3 {
    color: #22c55e;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.atm-welcome p {
    color: #94a3b8;
    font-size: 16px;
    margin: 0;
}

.atm-balance-display {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
    border: 1px solid #404040;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.atm-balance-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(34, 197, 94, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.atm-balance-display .balance-label-small {
    position: relative;
    z-index: 1;
}

.atm-balance-display .balance-amount-small {
    position: relative;
    z-index: 1;
    font-size: 32px;
}

/* Responsive ATM Layout */
@media (max-width: 1024px) {
    .atm-layout {
        flex-direction: column;
        align-items: center;
    }
    
    .atm-card-section {
        flex: none;
        width: 100%;
    }
    
    .atm-info-section {
        width: 100%;
    }
}

/* Single Page ATM Interface */
.atm-single-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    height: 100%;
}

.atm-card-section {
    flex: 0 0 450px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.atm-functions-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.atm-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.atm-action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.atm-action-card {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
}

.action-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #22c55e;
}

.withdraw-card .action-header {
    color: #ef4444;
}

.action-header i {
    font-size: 18px;
}

.action-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
}

.quick-amounts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.quick-amount-btn {
    background: #2d2d2d;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
}

.quick-amount-btn:hover:not(:disabled) {
    background: #ef4444;
    border-color: #ef4444;
}

.quick-amount-btn.deposit:hover:not(:disabled) {
    background: #22c55e;
    border-color: #22c55e;
}

.quick-amount-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.custom-amount-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.custom-amount-input {
    flex: 1;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 12px;
}

.custom-amount-input:focus {
    outline: none;
    border-color: #22c55e;
}

.custom-amount-input::placeholder {
    color: #64748b;
}

.custom-withdraw-btn {
    background: #ef4444;
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
}

.custom-withdraw-btn:hover:not(:disabled) {
    background: #dc2626;
}

.custom-withdraw-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.custom-deposit-btn {
    background: #22c55e;
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
}

.custom-deposit-btn:hover:not(:disabled) {
    background: #16a34a;
}

.custom-deposit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.atm-history-section {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
}

.atm-transactions {
    max-height: 200px;
    overflow-y: auto;
}

.atm-transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #2d2d2d;
}

.atm-transaction-item:last-child {
    border-bottom: none;
}

.atm-transaction-item .transaction-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.atm-transaction-item .transaction-icon {
    width: 32px;
    height: 32px;
    background: #2d2d2d;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #22c55e;
    font-size: 12px;
}

.atm-transaction-item .transaction-details h4 {
    color: #ffffff;
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 2px;
}

.atm-transaction-item .transaction-details p {
    color: #94a3b8;
    font-size: 11px;
    margin: 0;
}

.atm-transaction-item .transaction-amount {
    color: #22c55e;
    font-weight: 600;
    font-size: 13px;
}

.atm-transaction-item .transaction-amount.negative {
    color: #ef4444;
}

/* History Header */
.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.clear-all-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.clear-all-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.clear-all-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    opacity: 0.6;
}

.clear-all-btn i {
    font-size: 11px;
}

/* ATM Transfer Section */
.atm-transfer-section {
    background: #1a1a1a;
    border: 1px solid #2d2d2d;
    border-radius: 12px;
    padding: 20px;
    width: 400px;
}

.atm-transfer-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.atm-transfer-form .form-group-enhanced {
    margin-bottom: 0;
}

.atm-transfer-form .form-input-enhanced {
    padding: 12px 16px;
    font-size: 14px;
    background: #0f0f0f;
    border: 1px solid #2d2d2d;
    border-radius: 6px;
    color: #ffffff;
}

.atm-transfer-form .form-input-enhanced:focus {
    outline: none;
    border-color: #22c55e;
}

.atm-transfer-form .form-input-enhanced::placeholder {
    color: #64748b;
    font-weight: 400;
}

.atm-transfer-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    border: none;
    color: #ffffff;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 5px;
}

.atm-transfer-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
}

.atm-transfer-btn:disabled {
    background: #475569;
    cursor: not-allowed;
    opacity: 0.6;
}

.atm-transfer-btn i {
    font-size: 13px;
}

/* Header Responsive Design */
@media (max-width: 1200px) {
    .banking-header {
        padding: 16px 20px;
    }
    
    .bank-title {
        font-size: 18px;
    }
    
    .bank-subtitle {
        font-size: 10px;
    }
    
    .user-name {
        font-size: 15px;
        padding: 6px 14px;
        gap: 6px;
    }
    
    .user-avatar {
        width: 22px;
        height: 22px;
    }
    
    .user-avatar i {
        font-size: 11px;
    }
    
    .current-time {
        font-size: 13px;
        margin-right: 12px;
        padding: 6px 12px;
    }
}

@media (max-width: 900px) {
    .banking-header {
        padding: 14px 16px;
    }
    
    .header-center {
        display: none;
    }
    
    .header-left {
        flex: 1;
    }
    
    .header-right {
        flex: 0 0 auto;
    }
    
    .logo-icon {
        width: 36px;
        height: 36px;
    }
    
    .logo-icon i {
        font-size: 16px;
    }
    
    .bank-title {
        font-size: 16px;
    }
    
    .bank-subtitle {
        display: none;
    }
    
    .current-time {
        display: none;
    }
    
    .user-name {
        padding: 5px 12px;
        font-size: 14px;
        gap: 5px;
    }
    
    .user-avatar {
        width: 20px;
        height: 20px;
    }
    
    .user-avatar i {
        font-size: 10px;
    }
}

@media (max-width: 600px) {
    .banking-header {
        padding: 12px 12px;
    }
    
    .bank-logo {
        gap: 10px;
    }
    
    .logo-icon {
        width: 32px;
        height: 32px;
    }
    
    .logo-icon i {
        font-size: 14px;
    }
    
    .bank-title {
        font-size: 14px;
    }
    
    .close-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }
    
    .header-actions {
        gap: 8px;
    }
}

/* Responsive Single Page ATM */
@media (max-width: 1024px) {
    .atm-single-layout {
        flex-direction: column;
    }
    
    .atm-card-section {
        flex: none;
        width: 100%;
        align-items: center;
    }
    
    .atm-action-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-amounts {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .atm-transfer-section {
        width: 100%;
        max-width: 400px;
    }
} 