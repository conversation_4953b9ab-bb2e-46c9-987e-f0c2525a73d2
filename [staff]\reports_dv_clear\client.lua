AddEventHandler('onClientResourceStart', function(resName)
  if GetCurrentResourceName() ~= resName then return end
  TriggerEvent('chat:addSuggestion', '/report', 'Send a report to staff', {
    { name='message', help='Your message' }
  })
end)

-- client.lua

-- When this resource starts, register the /clear suggestion
AddEventHandler('onClientResourceStart', function(resName)
    if GetCurrentResourceName() ~= resName then
        return
    end
    TriggerEvent('chat:addSuggestion', '/clear', 'Clears your chat window')
end)

-- Register the /clear command
RegisterCommand('clear', function()
    -- This built‐in event clears the client’s chat history
    TriggerEvent('chat:clear')
end, false)


-- client.lua

-- Add the suggestion so it shows up in /autocomplete
AddEventHandler('onClientResourceStart', function(resName)
    if GetCurrentResourceName() ~= resName then 
        return 
    end
    TriggerEvent('chat:addSuggestion', '/clearall', 'Clears everyone’s chat (Admin/CL/leadership only)')
end)

-- (No need to register a separate client event to clear chat,
-- because we’ll just listen for the standard 'chat:clear' on each client.
-- FiveM’s chat resource already handles that.)


-- client.lua

-- 1. When the resource starts, register the /dv suggestion so it shows up in autocomplete
AddEventHandler('onClientResourceStart', function(resName)
    if GetCurrentResourceName() ~= resName then
        return
    end

    -- Register /dv with an optional radius parameter
    TriggerEvent('chat:addSuggestion', '/dv', 'Delete vehicles around you', {
        { name = 'radius', help = '(optional) radius in meters (default = 5)' }
    })
end)

-- 2. Listen for the server event that tells us to delete vehicles
RegisterNetEvent('dv:clientDelete')
AddEventHandler('dv:clientDelete', function(radius)
    -- Default sanity check
    radius = tonumber(radius) or 5
    if radius < 1 then radius = 5 end

    local playerPed = PlayerPedId()
    if not DoesEntityExist(playerPed) then
        return
    end

    local pCoords = GetEntityCoords(playerPed)
    local allVehicles = GetGamePool('CVehicle')
    local deletedCount = 0

    for _, veh in ipairs(allVehicles) do
        if DoesEntityExist(veh) then
            local vehCoords = GetEntityCoords(veh)
            local dist = #(pCoords - vehCoords)

            if dist <= radius then
                -- Request network control of the vehicle
                NetworkRequestControlOfEntity(veh)

                local start = GetGameTimer()
                while not NetworkHasControlOfEntity(veh) and DoesEntityExist(veh) and (GetGameTimer() - start) < 500 do
                    Wait(10)
                end

                if DoesEntityExist(veh) and NetworkHasControlOfEntity(veh) then
                    -- Mark as mission entity so we can delete
                    SetEntityAsMissionEntity(veh, true, true)
                    DeleteVehicle(veh)
                    deletedCount = deletedCount + 1
                end
            end
        end
    end

    -- Feedback to the user who ran /dv, with “DV:” in red (^1)
    if deletedCount == 0 then
        TriggerEvent('chat:addMessage', {
            args = { '^1DV:', 'No vehicles found within ' .. radius .. ' meters.' }
        })
    else
        TriggerEvent('chat:addMessage', {
            args = { '^1DV:', 'Deleted ' .. deletedCount .. ' vehicle(s) within ' .. radius .. ' meters.' }
        })
    end
end)
