

resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'

client_script "NativeUI.lua"

client_scripts { 
	'@fb_framework/initialise/cInit.lua',
	'@fb_framework/locale.lua',

	"client/config.lua",
	"client/hud.lua",
	"client/notification.lua",
	"client/skillbar.lua",
	"client/racing.lua",
}

server_scripts {
	'@fb_framework/shared/async.lua',
	'@fb_framework/database/lib/mysql.lua',
	'@fb_framework/locale.lua',
}

ui_page "public/index.html"

files {
	"build/build.js",

	"public/index.html",
	"public/img/*logo.png",
	"public/img/*.png",
	"build/img/*",
	"public/*.css",
	"public/*.js",

	"postals.json",
}

postal_file("postals.json")

exports {
	"getPlayerDirection",
	"getPlayerLocation",
	"getCoordsLocation",
	"skillbar"
}
