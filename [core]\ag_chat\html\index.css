
.color-0 {
  color: #ffffff;
}
.color-1 {
  color: #ff4444;
}
.color-2 {
  color: #99cc00;
}
.color-3 {
  color: #ffbb33;
}
.color-4 {
  color: #0099cc;
}
.color-5 {
  color: #33b5e5;
}
.color-6 {
  color: #aa66cc;
}
.color-8 {
  color: #cc0000;
}
.color-9 {
  color: #cc0068;
}

* {
  font-family: 'Lato', sans-serif;
  margin: 0;
  padding: 0;
}

body {
  margin: 12px;
}

.no-grow {
  flex-grow: 0;
}

em {
  font-style: normal;
}

#app {
  font-family: 'Lato', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: white;
}

/* 1) Make the chat window taller so more messages fit */
.chat-window {
  position: absolute;
  top: 1.5%;
  left: 0.8%;
  width: 24%;
  /* ↑ bump this up until the bottom of the window hits your green line */
  height: 37.5%;     /* was 29% — try 35%, 38%, etc. */
  max-width: 1000px;
  background-color: rgba(0, 0, 0, 0) !important;
  -webkit-animation-duration: 2s;
}



/* 3) (Optional) Give the message list some breathing room at the bottom */
.chat-window ul {
  padding-bottom: 0.5em;  /* so your last message isn’t jammed right at the bottom */
}


.chat-messages {
  position: relative;
  top: 70%;
  height: 95%;
  font-size: 1.60vh;
  margin: 1%;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  font-family: 'Lato', sans-serif;
  font-weight: normal;

  overflow-x: hidden;
  overflow-y: hidden;
}

.sysmessage {
  font-size: 1.60vh !important;
  color: rgb(187, 187, 187) !important;
  font-weight: bold !important;
}

/* Enhanced message styling */
.chat-messages li {
  margin-bottom: 2px;
  padding: 1px 0;
}

/* Staff message styling */
.staff-message {
  color: #ff0000 !important;
  font-weight: bold !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
}

/* Announcement styling */
.announcement-message {
  font-weight: bold !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
}

/* System message styling */
.system-message {
  font-weight: bold !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.9);
}

.chat-input {
  font-size: 1.60vh;
  position: absolute;
  /* position the input just below the chat history window:
     39% (window top) + 22% (window height) ≈ 61% */
  top: 65%;
  left: 0.8%;
  width: 24%;
  max-width: 1000px;
  box-sizing: border-box;
}

.prefix {
  font-size: 1.8vh;
  position: absolute;
  margin-top: 0.5%;
  left: 0.208%;
}

textarea {
  font-size: 1.65vh;
  display: block;
  box-sizing: border-box;
  padding: 1%;
  padding-left: 3.5%;
  color: white;
  background-color: rgba(0, 0, 0, 0.781) !important;
  width: 100%;
  border-width: 0;
  height: 3.15%;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Lato', sans-serif;
}

textarea:focus,
input:focus {
  outline: none;
}

.multiline {
  margin-left: 4%;
  text-indent: -1.2rem;
  white-space: pre-line;
}

.suggestions {
  list-style-type: none;
  padding: 0.5%;
  padding-left: 1.4%;
  font-size: 1.65vh;
  box-sizing: border-box;
  color: white;
  background-color: rgba(0, 0, 0, 0.781) !important;
  width: 100%;
}

.help {
  color: #b0bbbd;
}

.disabled {
  color: #b0bbbd;
}

.suggestion {
  margin-bottom: 0.5%;
}

.hidden {
  display: none;
}

/* change the dropdown background */
#suggestions {
  background-color: rgba(0, 0, 0, 0.781) !important;
  /* or any hex, rgb, rgba, etc. */
}

/* optional: tweak selected-item highlight */
#suggestions li.selected {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
