RegisterServerEvent('chat:init')
RegisterServerEvent('chat:addTemplate')
RegisterServerEvent('chat:addMessage')
RegisterServerEvent('chat:addSuggestion')
RegisterServerEvent('chat:removeSuggestion')
RegisterServerEvent('_chat:messageEntered')
RegisterServerEvent('chat:clear')
RegisterServerEvent('__cfx_internal:commandFallback')

-- Enhanced Sapphire Gaming Chat System
-- Export functions for other resources to use

-- Send staff message (all red, bold)
exports('sendStaffMessage', function(source, author, message)
    TriggerClientEvent('chat:addMessage', source, {
        templateId = 'staff',
        multiline = true,
        args = {author, message}
    })
end)

-- Send announcement (red system message with white body, bold)
exports('sendAnnouncement', function(source, message)
    TriggerClientEvent('chat:addMessage', source, {
        templateId = 'announcement',
        multiline = true,
        args = {message}
    })
end)

-- Send system message (red system message with white body, bold)
exports('sendSystemMessage', function(source, message)
    TriggerClientEvent('chat:addMessage', source, {
        templateId = 'system',
        multiline = true,
        args = {message}
    })
end)



-- Send roleplay action (/me)
exports('sendMeMessage', function(source, author, action)
    TriggerClientEvent('chat:addMessage', source, {
        templateId = 'me',
        multiline = true,
        args = {author, action}
    })
end)

-- Send roleplay description (/do)
exports('sendDoMessage', function(source, author, description)
    TriggerClientEvent('chat:addMessage', source, {
        templateId = 'do',
        multiline = true,
        args = {author, description}
    })
end)


AddEventHandler('__cfx_internal:commandFallback', function(command)
    local name = GetPlayerName(source)

    TriggerEvent('chatMessage', source, name, '/' .. command)

    if not WasEventCanceled() then
        TriggerClientEvent('chatMessage', -1, name, { 255, 255, 255 }, '/' .. command) 
    end

    CancelEvent()
end)

-- command suggestions for clients
local function refreshCommands(player)
    if GetRegisteredCommands then
        local registeredCommands = GetRegisteredCommands()

        local suggestions = {}

        for _, command in ipairs(registeredCommands) do
            if IsPlayerAceAllowed(player, ('command.%s'):format(command.name)) then
                table.insert(suggestions, {
                    name = '/' .. command.name,
                    help = ''
                })
            end
        end

        TriggerClientEvent('chat:addSuggestions', player, suggestions)
    end
end

AddEventHandler('chat:init', function()
    refreshCommands(source)
end)

AddEventHandler('onServerResourceStart', function(resName)
    Wait(500)

    for _, player in ipairs(GetPlayers()) do
        refreshCommands(player)
    end
end)
