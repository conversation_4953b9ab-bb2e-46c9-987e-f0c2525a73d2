Config = Config or {}

-- Sapphire Gaming Staff Groups Configuration
Config.StaffGroups = {
    ['god'] = {
        level = 0,
        power = 100,
        canUseNames = true,
        canKick = true,
        canBan = true,
        canTeleport = true,
        canBring = true,
        canAnnounce = true,
        canViewLogs = true,
        canResourceControl = true,
        canKillAll = true,
        canReviveAll = true,
        canMessageAll = true,
        canMassDeleteEntities = true,
        canSetViewDistance = true,
        canCopyEntityInfo = true,
        canFreeAimMode = true,
        canDisplayVehicles = true,
        canDisplayPeds = true,
        canDisplayObjects = true,
        canDeleteClosestPed = true,
        canDeleteClosestObject = true,
        canClearReports = true,
        canClearAdminChat = true,
        canViewAllCharacters = true,
        canDeleteCharacter = true,
        canNoClip = true,
        canFreeze = true,
        canWeather = true,
        canTime = true,
        canGiveTakeMoney = true,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = true,
        canDeleteVehicle = true,
        canGiveItem = true,
        canRemoveItem = true,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = true,
        canViewVehicleInfo = true,
        canViewItemInfo = true,
        canLeaderboardInfo = true
    },
    ['leadership'] = {
        level = 1,
        power = 90,
        canUseNames = true,
        canKick = true,
        canBan = true,
        canTeleport = true,
        canBring = true,
        canAnnounce = true,
        canViewLogs = true,
        canResourceControl = true,
        canKillAll = true,
        canReviveAll = true,
        canDeleteVehiclesRadius = true,
        canMessageAll = true,
        canMassDeleteEntities = true,
        canSetViewDistance = true,
        canCopyEntityInfo = true,
        canFreeAimMode = true,
        canDisplayVehicles = true,
        canDisplayPeds = true,
        canDisplayObjects = true,
        canDeleteClosestPed = true,
        canDeleteClosestObject = true,
        canClearReports = false,
        canClearAdminChat = false,
        canViewAllCharacters = true,
        canDeleteCharacter = true,
        canNoClip = true,
        canFreeze = true,
        canWeather = true,
        canTime = true,
        canGiveTakeMoney = true,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = true,
        canDeleteVehicle = true,
        canGiveItem = true,
        canRemoveItem = true,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = true,
        canViewVehicleInfo = true,
        canViewItemInfo = true,
        canLeaderboardInfo = true
    },
    ['cl'] = {
        level = 2,
        power = 80,
        canUseNames = true,
        canKick = true,
        canBan = true,
        canTeleport = true,
        canBring = true,
        canAnnounce = true,
        canViewLogs = true,
        canResourceControl = false,
        canKillAll = true,
        canReviveAll = true,
        canDeleteVehiclesRadius = true,
        canMessageAll = true,
        canMassDeleteEntities = false,
        canSetViewDistance = false,
        canCopyEntityInfo = true,
        canFreeAimMode = false,
        canDisplayVehicles = true,
        canDisplayPeds = true,
        canDisplayObjects = true,
        canDeleteClosestPed = true,
        canDeleteClosestObject = true,
        canClearReports = false,
        canClearAdminChat = false,
        canViewAllCharacters = true,
        canDeleteCharacter = true,
        canNoClip = true,
        canFreeze = true,
        canWeather = true,
        canTime = true,
        canGiveTakeMoney = true,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = true,
        canDeleteVehicle = true,
        canGiveItem = true,
        canRemoveItem = true,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = true,
        canViewVehicleInfo = true,
        canViewItemInfo = true,
        canLeaderboardInfo = true
    },
    ['senioradmin'] = {
        level = 3,
        power = 75,
        canUseNames = true,
        canKick = true,
        canBan = true,
        canTeleport = true,
        canBring = true,
        canAnnounce = true,
        canViewLogs = true,
        canResourceControl = false,
        canKillAll = true,
        canReviveAll = true,
        canDeleteVehiclesRadius = true,
        canMessageAll = true,
        canMassDeleteEntities = false,
        canSetViewDistance = false,
        canCopyEntityInfo = true,
        canFreeAimMode = false,
        canDisplayVehicles = true,
        canDisplayPeds = true,
        canDisplayObjects = true,
        canDeleteClosestPed = true,
        canDeleteClosestObject = true,
        canClearReports = false,
        canClearAdminChat = false,
        canViewAllCharacters = true,
        canDeleteCharacter = false,
        canNoClip = true,
        canFreeze = true,
        canWeather = true,
        canTime = true,
        canGiveTakeMoney = false,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = true,
        canDeleteVehicle = true,
        canGiveItem = true,
        canRemoveItem = true,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = true,
        canViewVehicleInfo = true,
        canViewItemInfo = true,
        canLeaderboardInfo = true
    },
    ['admin'] = {
        level = 4,
        power = 70,
        canUseNames = true,
        canKick = true,
        canBan = true,
        canTeleport = true,
        canBring = true,
        canAnnounce = true,
        canViewLogs = true,
        canResourceControl = false,
        canKillAll = false,
        canReviveAll = true,
        canDeleteVehiclesRadius = false,
        canMessageAll = true,
        canMassDeleteEntities = false,
        canSetViewDistance = false,
        canCopyEntityInfo = true,
        canFreeAimMode = false,
        canDisplayVehicles = true,
        canDisplayPeds = true,
        canDisplayObjects = true,
        canDeleteClosestPed = true,
        canDeleteClosestObject = true,
        canClearReports = false,
        canClearAdminChat = false,
        canViewAllCharacters = true,
        canDeleteCharacter = false,
        canNoClip = true,
        canFreeze = true,
        canChangeWeather = true,
        canChangeTime = true,
        canGiveTakeMoney = false,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = true,
        canDeleteVehicle = true,
        canGiveItem = true,
        canRemoveItem = true,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = false,
        canViewVehicleInfo = true,
        canViewItemInfo = true,
        canLeaderboardInfo = true
    },
    ['mod'] = {
        level = 5,
        power = 50,
        canUseNames = true,
        canKick = true,
        canBan = false,
        canTeleport = true,
        canBring = true,
        canAnnounce = false,
        canViewLogs = false,
        canResourceControl = false,
        canKillAll = false,
        canReviveAll = false,
        canDeleteVehiclesRadius = false,
        canMessageAll = false,
        canMassDeleteEntities = false,
        canSetViewDistance = false,
        canCopyEntityInfo = false,
        canFreeAimMode = false,
        canDisplayVehicles = false,
        canDisplayPeds = false,
        canDisplayObjects = false,
        canDeleteClosestPed = false,
        canDeleteClosestObject = false,
        canClearReports = false,
        canClearAdminChat = false,
        canViewAllCharacters = false,
        canDeleteCharacter = false,
        canNoClip = true,
        canFreeze = true,
        canWeather = false,
        canTime = false,
        canGiveTakeMoney = false,
        canWarn = true,
        canRevive = true,
        canFoodAndWater = true,
        canSpawnVehicle = false,
        canDeleteVehicle = false,
        canGiveItem = false,
        canRemoveItem = false,
        canSpectate = true,
        canScreenshot = true,
        canAdminChat = true,
        canViewReports = true,
        canClaimReport = true,
        canDeleteReport = true,
        canCheckWarns = true,
        canUnban = false,
        canViewVehicleInfo = false,
        canViewItemInfo = false,
        canLeaderboardInfo = false
    }
}

-- Names System Configuration
Config.Names = {
    enabled = true,
    showHealthBar = true,
    showSteamName = true,
    showServerId = true,
    maxDistance = 50.0,
    staffNameColor = {255, 0, 0}, -- Red color for staff with names active
    playerNameColor = {255, 255, 255}, -- White color for regular players
    healthBarColor = {0, 255, 0}, -- Green color for health bar
    healthBarBackgroundColor = {255, 0, 0} -- Red color for health bar background
}

-- Logging Configuration
Config.Logging = {
    enabled = true,
    logNamesActivation = true,
    logStaffActions = true,
    logAnnouncements = true,
    maxLogEntries = 100
}

-- UI Configuration
Config.UI = {
    refreshInterval = 5000, -- 5 seconds
    showOnlineStaff = true,
    showPlayerCount = true,
    showServerUptime = true
}

-- Ban System Configuration (if you want to implement custom bans)
Config.BanSystem = {
    enabled = false, -- Set to true if you want to use custom ban system
    defaultBanTime = 86400, -- 24 hours in seconds
    maxBanTime = 2592000, -- 30 days in seconds
    banReasons = {
        'Cheating/Hacking',
        'Exploiting',
        'RDM/VDM',
        'Fail RP',
        'Toxicity',
        'Other'
    }
}

-- Teleport Configuration
Config.Teleport = {
    fadeScreen = true,
    fadeTime = 1000,
    showNotification = true
}

-- Announcement Configuration
Config.Announcements = {
    prefix = '[SAPPHIRE GAMING STAFF]',
    color = {59, 130, 246}, -- Blue color for Sapphire Gaming
    duration = 10000 -- 10 seconds
}

-- Database Configuration
Config.Database = {
    usePlayerIds = true, -- Use TX default player IDs instead of account IDs
    logsTable = 'sg_staff_logs',
    bansTable = 'sg_staff_bans',
    warningsTable = 'sg_staff_warnings',
    reportsTable = 'sg_staff_reports',
    chatTable = 'sg_staff_chat',
    notesTable = 'sg_staff_notes',
    sessionsTable = 'sg_staff_sessions',
    vehicleSpawnsTable = 'sg_staff_vehicle_spawns',
    moneyLogsTable = 'sg_staff_money_logs',
    itemLogsTable = 'sg_staff_item_logs'
}

-- Discord Webhook Configuration
Config.Discord = {
    enabled = true,
    webhook = 'https://discord.com/api/webhooks/1396394563660288162/FreMejYDEolJC6JxBj3Qkrl-U1Sv90YgYDTCtu9kjMsTPRW55_79ZfhmH2OJBFQPxahc', -- Replace with your Discord webhook URL
    botName = 'Sapphire Gaming Staff',
    botAvatar = 'https://cdn.discordapp.com/attachments/870094209783308299/870104723338973224/Logotype_-_Display_Picture_-_Stylized_-_Red.png',
    colors = {
        ban = ********, -- Red
        unban = 65280, -- Green
        kick = ********, -- Orange
        warn = ********, -- Yellow
        teleport = 3447003, -- Blue
        money = 15844367, -- Gold
        vehicle = 10181046, -- Purple
        item = 3066993, -- Dark Green
        general = 8359053 -- Gray
    }
}

-- Admin Panel Configuration
Config.AdminPanel = {
    key = 'F1', -- Key to open admin panel
    noclipKey = 'F2', -- Key for noclip
    maxPlayers = 128, -- Server max players
    refreshInterval = 5000, -- Auto refresh interval in ms
    saveToJSON = false, -- Save reports/chat to JSON files
    screenshotWebhook = 'CHANGEME', -- Screenshot webhook
    enableReports = true,
    enableAdminChat = true,
    enableWarnings = true,
    enableNotes = true
}

-- Vehicle Spawn Configuration
Config.Vehicles = {
    spawnDistance = 5.0, -- Distance from player to spawn vehicle
    deleteDistance = 10.0, -- Distance to delete vehicles
    maxVehiclesPerPlayer = 5, -- Max vehicles a player can have spawned
    allowedCategories = {
        'compacts', 'sedans', 'suvs', 'coupes', 'muscle', 'sportsclassics',
        'sports', 'super', 'motorcycles', 'offroad', 'industrial', 'utility',
        'vans', 'cycles', 'boats', 'helicopters', 'planes', 'service',
        'emergency', 'military', 'commercial', 'trains'
    }
}

-- Money Configuration
Config.Money = {
    maxAmount = 10000000, -- Maximum amount that can be given/taken at once
    logAllTransactions = true,
    allowNegativeBalance = false
}

-- Radius Actions Configuration
Config.RadiusActions = {
    -- Available radius options for revive all and delete vehicles
    radiusOptions = {
        { label = "10m", value = 10.0 },
        { label = "25m", value = 25.0 },
        { label = "50m", value = 50.0 },
        { label = "100m", value = 100.0 },
        { label = "200m", value = 200.0 },
        { label = "500m", value = 500.0 }
    },
    defaultRadius = 50.0, -- Default radius if none selected
    maxRadius = 1000.0 -- Maximum allowed radius
}

-- Item Configuration
Config.Items = {
    maxQuantity = 1000, -- Maximum quantity that can be given/taken at once
    logAllTransactions = true,
    blacklistedItems = {
        -- Add items that shouldn't be spawnable
    }
}

-- Spectate Configuration
Config.Spectate = {
    showHUD = true,
    showPlayerInfo = true,
    allowSpectateStaff = false -- Whether staff can spectate other staff
}

-- Screenshot Configuration
Config.Screenshot = {
    enabled = true,
    quality = 0.8, -- 0.1 to 1.0
    encoding = 'jpg', -- jpg or png
    webhook = 'CHANGEME' -- Screenshot webhook URL
}

-- Noclip Configuration
Config.NoClip = {
    speed = {
        normal = 1.0,
        fast = 2.5,
        faster = 5.0,
        fastest = 10.0
    },
    controls = {
        toggle = 'F2',
        speedUp = 'LSHIFT',
        speedDown = 'LCTRL',
        forward = 'W',
        backward = 'S',
        left = 'A',
        right = 'D',
        up = 'SPACE',
        down = 'LCTRL'
    }
}

-- Report Configuration
Config.Reports = {
    maxReports = 100, -- Maximum reports to keep in database
    autoDeleteOld = true, -- Auto delete old reports
    deleteAfterDays = 30, -- Delete reports older than X days
    allowAnonymous = false, -- Allow anonymous reports
    categories = {
        'Player Report',
        'Bug Report',
        'Suggestion',
        'Other'
    }
}

-- Ban Configuration
Config.Bans = {
    defaultDuration = 86400, -- 24 hours in seconds
    maxDuration = 2592000, -- 30 days in seconds
    allowPermanent = true,
    checkOnJoin = true,
    announceInChat = true,
    tagEveryone = false, -- Tag everyone in Discord on ban
    reasons = {
        'Cheating/Hacking',
        'Exploiting',
        'RDM/VDM',
        'Fail RP',
        'Toxicity',
        'Griefing',
        'Meta Gaming',
        'Power Gaming',
        'LTAP (Leaving to Avoid Punishment)',
        'Other'
    }
}

-- Warning Configuration
Config.Warnings = {
    maxWarnings = 3, -- Auto ban after X warnings
    autoBanDuration = 86400, -- Auto ban duration in seconds
    deleteAfterDays = 90, -- Delete warnings older than X days
    reasons = {
        'Minor RP Violation',
        'Chat Violation',
        'Minor Griefing',
        'Fail RP',
        'Other'
    }
}
