-- server.lua
-- Range viewer command restricted to mod, admin, cl, owner groups

ESX = nil
TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Allowed groups for the range command
local allowedGroups = {
    mod = true,
    admin = true,
    senioradmin = true,
    cl = true,
    leadership = true
}

-- Helper to check if a player has permission
local function hasPermission(xPlayer)
    return allowedGroups[xPlayer.getGroup() or ''] == true
end

-- Register the /range command
ESX.RegisterCommand('range', {'mod', 'admin', 'cl', 'owner'}, function(xPlayer, args, showError)
    -- Permission is already handled by ESX.RegisterCommand with the groups parameter
    -- Just trigger the client event to open the UI
    TriggerClientEvent('vc_range:openUI', xPlayer.source)
end, false, {
    help = 'Open the range viewer UI',
    arguments = {}
})

print('[vc_range] Resource loaded successfully')
