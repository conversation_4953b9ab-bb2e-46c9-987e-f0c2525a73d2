// html/spawn.js
let selectedOption = null;
let selectedSpawnId = null;

window.addEventListener('message', e => {
  if (e.data.action === 'show') {
    // HEADER
    document.getElementById('player-name').textContent = e.data.playerName;

    // OPTIONS
    const confirmBtn = document.querySelector('.btn-confirm');
    const locButtons = Array.from(document.querySelectorAll('.btn-last, .btn-spawn'));

    // reset
    selectedOption = null;
    selectedSpawnId = null;
    confirmBtn.classList.remove('enabled');
    confirmBtn.classList.add('disabled');
    confirmBtn.onclick = null;
    locButtons.forEach(el => el.classList.remove('selected'));

    // wire clicks
    locButtons.forEach(el => {
      el.onclick = () => {
        locButtons.forEach(x => x.classList.remove('selected'));
        el.classList.add('selected');
        selectedOption = el.dataset.option;
        selectedSpawnId = el.dataset.spawnId || null;

        confirmBtn.classList.remove('disabled');
        confirmBtn.classList.add('enabled');
        confirmBtn.onclick = () => sendSelection(selectedOption, selectedSpawnId);
      };
    });

    // show menu
    document.getElementById('spawn-menu').style.display = 'block';
  }

  if (e.data.action === 'hide') {
    document.getElementById('spawn-menu').style.display = 'none';
  }
});

function sendSelection(option, spawnId = null) {
  const data = { option };
  if (spawnId) {
    data.spawnId = spawnId;
  }
  
  fetch(`https://${GetParentResourceName()}/spawnSelection`, {
    method: 'POST',
    headers: { 'Content-Type':'application/json' },
    body: JSON.stringify(data)
  });
}
