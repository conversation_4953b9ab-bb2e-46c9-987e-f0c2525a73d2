* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: transparent;
    overflow: hidden;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.staff-panel {
    position: fixed;
    top: 5vh;
    left: 5vw;
    width: 90vw;
    height: 90vh;
    background: #0f1419;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    transition: opacity 0.2s ease, transform 0.2s ease;
    border-radius: 12px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.7),
        0 0 0 1px rgba(30, 58, 138, 0.3);
    overflow: hidden;
}

.staff-panel.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.95);
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 28px;
    background: #1e293b;
    border-bottom: 1px solid rgba(30, 58, 138, 0.3);
    min-height: 70px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 32px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo i {
    font-size: 32px;
    color: #3b82f6; /* Blue color for Sapphire Gaming */
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text h2 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2px;
}

.logo-text span {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.welcome-message h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
    letter-spacing: -0.01em;
    color: rgba(255, 255, 255, 0.95);
}

.welcome-message p {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 24px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding: 12px 16px;
    gap: 12px;
    transition: background-color 0.2s ease;
    backdrop-filter: blur(10px);
}

.search-container:focus-within {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-container i {
    color: rgba(255, 255, 255, 0.4);
    font-size: 14px;
}

.search-container input {
    background: transparent;
    border: none;
    outline: none;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    width: 200px;
}

.search-container input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.search-shortcut {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.06);
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.online-count {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.3);
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #3b82f6;
    backdrop-filter: blur(10px);
}

.online-count i {
    font-size: 16px;
}

.close-btn {
    background: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    width: 44px;
    height: 44px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(239, 68, 68, 0.25);
    border-color: rgba(239, 68, 68, 0.5);
    transform: scale(1.05);
}

.panel-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    background: #0f1419;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #1e293b;
    border-right: 1px solid rgba(30, 58, 138, 0.3);
    display: flex;
    flex-direction: column;
    padding: 20px 0;
}

.nav-menu {
    flex: 1;
    padding: 0 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 14px 18px;
    margin-bottom: 4px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #cccccc;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.nav-item:hover {
    background: rgba(64, 64, 64, 0.5);
    color: #ffffff;
    transform: translateX(2px);
}

.nav-item.active {
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.4);
    color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 0 2px 2px 0;
}

.nav-item i {
    width: 18px;
    text-align: center;
    font-size: 16px;
}

/* Staff Info */
.staff-info {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.06);
    display: flex;
    align-items: center;
    gap: 14px;
    background: rgba(255, 255, 255, 0.02);
}

.staff-avatar {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #3b82f6;
}

.staff-details h4 {
    font-size: 15px;
    margin-bottom: 4px;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.staff-details p {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 28px;
    overflow-y: auto;
    background: #0f1419;
}

.section {
    display: none;
    animation: fadeIn 0.2s ease-out;
}

.section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.section-header h3 {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: -0.02em;
    color: rgba(255, 255, 255, 0.95);
}

.refresh-btn, .action-btn, .send-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 1);
    padding: 12px 18px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.refresh-btn:hover, .action-btn:hover, .send-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.15);
}

.refresh-btn:disabled, .action-btn:disabled, .send-btn:disabled {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.refresh-btn:disabled:hover, .action-btn:disabled:hover, .send-btn:disabled:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    transform: none;
    box-shadow: none;
}

/* Confirmation state styling */
.action-btn[data-confirm-clear="true"] {
    background: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.4) !important;
    color: #ef4444 !important;
    /* Removed infinite animation to reduce lag */
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin-bottom: 32px;
}

.stat-card {
    background: #1e293b;
    border: 1px solid rgba(30, 58, 138, 0.3);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 18px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(156, 163, 175, 0.2), transparent);
}

.stat-card:hover {
    background: #334155;
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #3b82f6;
    backdrop-filter: blur(10px);
}

.stat-info h4 {
    font-size: 28px;
    font-weight: 800;
    margin-bottom: 4px;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-info p {
    color: rgba(255, 255, 255, 0.5);
    font-size: 13px;
    font-weight: 500;
}

/* Recent Activity */
.recent-activity {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 24px;
}

.recent-activity h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.9);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    font-size: 16px;
}

.activity-content p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 4px;
    font-weight: 500;
}

.activity-time {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* Players List */
.players-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;
}

.player-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.player-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.player-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #3b82f6;
    flex-shrink: 0;
}

.player-details {
    flex: 1;
}

.player-details h5 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    color: rgba(255, 255, 255, 0.95);
}

.player-details p {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
}

.player-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.online {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-badge.staff {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Admin Only Notice */
.admin-only-notice {
    color: rgba(59, 130, 246, 0.8);
    font-size: 14px;
    font-weight: 500;
    margin-top: 4px;
}

/* Server Tools */
.server-tools, .moderation-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.tool-card {
    background: #1e293b;
    border: 1px solid rgba(30, 58, 138, 0.3);
    border-radius: 16px;
    padding: 24px;
    display: flex;
    align-items: flex-start;
    gap: 18px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.tool-card.admin-only {
    border-color: rgba(239, 68, 68, 0.3);
    background: rgba(239, 68, 68, 0.05);
}

.tool-card:hover {
    background: #334155;
    border-color: rgba(59, 130, 246, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.tool-card.admin-only:hover {
    border-color: rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
}

.tool-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #3b82f6;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.tool-info {
    flex: 1;
}

.tool-info h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.95);
}

.tool-info p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.4;
}

/* Time and Weather Controls */
.time-controls, .weather-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.time-controls input, .weather-controls select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 80px;
}

.time-controls input::placeholder {
    color: rgba(255, 255, 255, 0.4);
}

.weather-controls select {
    min-width: 140px;
}

.weather-controls select option {
    background: #2a2a2a;
    color: white;
}

/* Danger button styling */
.action-btn.danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.action-btn.danger:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);
}

/* Warning button styling */
.action-btn.warning {
    background: rgba(168, 85, 247, 0.1);
    border-color: rgba(168, 85, 247, 0.3);
    color: #a855f7;
}

.action-btn.warning:hover {
    background: rgba(168, 85, 247, 0.2);
    border-color: rgba(168, 85, 247, 0.5);
    box-shadow: 0 8px 25px rgba(168, 85, 247, 0.2);
}

/* Online Staff Sidebar */
.online-staff-sidebar {
    width: 320px;
    background: #1e293b;
    border-left: 1px solid rgba(30, 58, 138, 0.3);
    padding: 25px 20px;
    flex-shrink: 0;
}

/* Hide any Online Staff sections that might appear in dashboard content */
.section .online-staff,
.section [data-section="online-staff"],
.dashboard-section .online-staff,
#dashboard-section .online-staff,
#dashboard-section h4:contains("Online Staff"),
.content-area h4:contains("Online Staff"),
.section h4:contains("Online Staff") {
    display: none !important;
}

/* More aggressive hiding of unwanted Online Staff content in dashboard */
#dashboard-section > div:has(h4:contains("Online Staff")),
.content-area > div:has(h4:contains("Online Staff")) {
    display: none !important;
}

/* Ensure only the sidebar Online Staff is visible */
.online-staff-sidebar h4 {
    display: block !important;
    margin-bottom: 20px;
    font-size: 18px;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
}

.staff-member {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.staff-member:hover {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.staff-member-avatar {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #3b82f6;
}

.staff-member-info h6 {
    margin-bottom: 2px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

.staff-member-info p {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Player Info Section */
#player-info-section {
    animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.player-info-content {
    padding: 20px;
}

.player-info-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-info-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(29, 78, 216, 0.3));
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #3b82f6;
}

.player-info-basic h4 {
    margin: 0 0 5px 0;
    font-size: 24px;
    color: #ffffff;
    font-weight: 600;
}

.player-info-basic p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.info-item span {
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.action-group {
    margin-bottom: 25px;
}

.action-group h5 {
    margin: 0 0 15px 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 8px;
}

.action-group .action-btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* Logs Styles */
.logs-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.logs-filters {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.logs-filters select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-family: inherit;
    font-size: 14px;
}

.logs-filters select option {
    background: #2a2a2a;
    color: white;
}

.logs-list {
    max-height: 400px;
    overflow-y: auto;
}

.log-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.log-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.log-item:last-child {
    border-bottom: none;
}

.log-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.log-icon.names { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.log-icon.kick { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
.log-icon.ban { background: rgba(220, 38, 38, 0.2); color: #dc2626; }
.log-icon.teleport { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
.log-icon.bring { background: rgba(168, 85, 247, 0.2); color: #a855f7; }
.log-icon.announcement { background: rgba(168, 85, 247, 0.2); color: #a855f7; }

.log-content p {
    margin-bottom: 4px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.log-time {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* Names toggle button states */
.names-enabled {
    background: rgba(34, 197, 94, 0.2) !important;
    border-color: rgba(34, 197, 94, 0.3) !important;
    color: #22c55e !important;
}

.names-enabled:hover {
    background: rgba(34, 197, 94, 0.3) !important;
}

/* Staff name red color for names enabled */
.staff-name-red {
    color: #ef4444 !important;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Admin-only elements - hidden by default until permission check */
.admin-only {
    display: none;
}

.admin-only.show {
    display: flex;
}

/* Reports Styles */
.reports-container, .bans-container, .warnings-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.reports-filters, .bans-search, .warnings-search {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.reports-filters select, .bans-search input, .warnings-search input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    font-family: inherit;
    font-size: 14px;
    width: 100%;
}

.reports-list, .bans-list, .warnings-list {
    max-height: 400px;
    overflow-y: auto;
}

.report-item, .ban-item, .warning-item {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.report-item:hover, .ban-item:hover, .warning-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.report-item:last-child, .ban-item:last-child, .warning-item:last-child {
    border-bottom: none;
}

.report-header, .ban-header, .warning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.report-header h5, .ban-header h5, .warning-header h5 {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.95);
}

.report-status, .ban-status {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.report-status.open {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.report-status.claimed {
    background: rgba(168, 85, 247, 0.2);
    color: #a855f7;
    border: 1px solid rgba(168, 85, 247, 0.3);
}

.report-status.closed {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.ban-status.active {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.ban-status.expired {
    background: rgba(107, 114, 128, 0.2);
    color: #6b7280;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.report-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

.ban-details, .warning-details {
    margin-top: 8px;
}

.ban-details p, .warning-details p {
    margin: 4px 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

/* Vehicle and Item Management Styles */
.vehicles-container, .items-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.vehicle-spawn, .item-give {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.vehicle-spawn h4, .item-give h4 {
    margin: 0 0 16px 0;
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
}

.spawn-controls, .give-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.spawn-controls select, .spawn-controls input, .give-controls input, .give-controls select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px 12px;
    color: white;
    font-family: inherit;
    font-size: 14px;
}

.spawn-controls select option, .give-controls select option {
    background: #2a2a2a;
    color: white;
}

.vehicle-list, .item-list {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
}

.vehicle-list h4, .item-list h4 {
    margin: 0 0 16px 0;
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
}

.vehicle-logs, .item-logs {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Money Controls */
.money-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.money-controls select, .money-controls input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 6px 10px;
    color: white;
    font-size: 12px;
    min-width: 80px;
}

.money-controls select option {
    background: #2a2a2a;
    color: white;
}

/* Warning date */
.warning-date {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* Item Search Dropdown */
.item-search-container {
    position: relative;
    width: 100%;
}

.item-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1e293b;
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.item-dropdown.show {
    display: block;
}

.dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(59, 130, 246, 0.1);
}

.dropdown-item.selected {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.item-name {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.item-weight {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

.dropdown-item.selected .item-name {
    color: #3b82f6;
}

.dropdown-item.selected .item-weight {
    color: rgba(59, 130, 246, 0.7);
}

/* No results message */
.dropdown-no-results {
    padding: 12px 16px;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    font-style: italic;
}
