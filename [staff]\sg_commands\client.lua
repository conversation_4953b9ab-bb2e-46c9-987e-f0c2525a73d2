-- =====================================================
-- Sapphire Gaming Commands - Client Script
-- Handles client-side command functionality
-- =====================================================

-- Prevent loading on the server
if IsDuplicityVersion() then return end

print('^3[Sapphire Gaming Commands]^0 Loading client-side command handlers...')

-- =====================================================
-- CHAT SUGGESTIONS
-- =====================================================

-- Admin Commands
TriggerEvent('chat:addSuggestion', '/setjob', 'Set a player\'s job', {
    { name = 'player', help = 'Player ID or name' },
    { name = 'job', help = 'Job name (unemployed, police, ambulance, etc.)' },
    { name = 'grade', help = 'Job grade (optional, default: 0)' }
})

TriggerEvent('chat:addSuggestion', '/setgroup', 'Change a player\'s group', {
    { name = 'player', help = 'Player ID or name' },
    { name = 'group', help = 'Group name (user, mod, admin, senioradmin, cl, leadership, god)' }
})

TriggerEvent('chat:addSuggestion', '/kick', 'Kick a player from the server', {
    { name = 'player', help = 'Player ID or name' },
    { name = 'reason', help = 'Reason for kick (optional)' }
})

-- Staff Commands
TriggerEvent('chat:addSuggestion', '/heal', 'Heal a player', {
    { name = 'player', help = 'Player ID or name (optional, heals yourself if not specified)' }
})

TriggerEvent('chat:addSuggestion', '/revive', 'Revive a player', {
    { name = 'player', help = 'Player ID or name (optional, revives yourself if not specified)' }
})

TriggerEvent('chat:addSuggestion', '/tp', 'Teleport to player or coordinates', {
    { name = 'player/x', help = 'Player ID/name or X coordinate' },
    { name = 'y', help = 'Y coordinate (if using coordinates)' },
    { name = 'z', help = 'Z coordinate (if using coordinates)' }
})

TriggerEvent('chat:addSuggestion', '/bring', 'Bring a player to you', {
    { name = 'player', help = 'Player ID or name' }
})

TriggerEvent('chat:addSuggestion', '/goto', 'Teleport to a player', {
    { name = 'player', help = 'Player ID or name' }
})

-- Information Commands
TriggerEvent('chat:addSuggestion', '/group', 'Show your current group', {})
TriggerEvent('chat:addSuggestion', '/groups', 'Show all valid groups (admin only)', {})
TriggerEvent('chat:addSuggestion', '/job', 'Show your current job', {})
TriggerEvent('chat:addSuggestion', '/jobs', 'Show all valid jobs (admin only)', {})
TriggerEvent('chat:addSuggestion', '/players', 'Show online players (staff only)', {})

-- Debug Commands
TriggerEvent('chat:addSuggestion', '/testsetgroup', 'Test setgroup permissions', {})

-- Legacy Commands (if they exist)
TriggerEvent('chat:addSuggestion', '/range', 'Toggle range viewer', {})

-- =====================================================
-- CLIENT EVENT HANDLERS
-- =====================================================

-- Heal player
RegisterNetEvent('sg_commands:heal')
AddEventHandler('sg_commands:heal', function()
    local playerPed = PlayerPedId()

    -- Heal player
    SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))

    -- Remove all damage
    ClearPedBloodDamage(playerPed)
    ResetPedVisibleDamage(playerPed)
    ClearPedLastWeaponDamage(playerPed)

    -- Show notification
    SetNotificationTextEntry("STRING")
    AddTextComponentString("You have been healed!")
    DrawNotification(false, false)
end)

-- Revive player
RegisterNetEvent('sg_commands:revive')
AddEventHandler('sg_commands:revive', function()
    local playerPed = PlayerPedId()
    
    -- Revive player
    if IsEntityDead(playerPed) then
        NetworkResurrectLocalPlayer(GetEntityCoords(playerPed), true, true, false)
    end
    
    -- Heal after revive
    SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))
    ClearPedBloodDamage(playerPed)
    ResetPedVisibleDamage(playerPed)
    SetPedArmour(playerPed, 100)
    
    -- Show notification
    SetNotificationTextEntry("STRING")
    AddTextComponentString("You have been revived!")
    DrawNotification(false, false)
end)

-- Teleport to player
RegisterNetEvent('sg_commands:teleportToPlayer')
AddEventHandler('sg_commands:teleportToPlayer', function(targetSource)
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetSource))
    if targetPed and targetPed ~= 0 then
        local coords = GetEntityCoords(targetPed)
        SetEntityCoords(PlayerPedId(), coords.x, coords.y, coords.z, false, false, false, true)
    end
end)

-- Teleport to coordinates
RegisterNetEvent('sg_commands:teleportToCoords')
AddEventHandler('sg_commands:teleportToCoords', function(x, y, z)
    SetEntityCoords(PlayerPedId(), x, y, z, false, false, false, true)
end)

-- Bring player to admin
RegisterNetEvent('sg_commands:bringPlayer')
AddEventHandler('sg_commands:bringPlayer', function(adminSource)
    local adminPed = GetPlayerPed(GetPlayerFromServerId(adminSource))
    if adminPed and adminPed ~= 0 then
        local coords = GetEntityCoords(adminPed)
        SetEntityCoords(PlayerPedId(), coords.x + 1.0, coords.y, coords.z, false, false, false, true)
    end
end)

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to show notification
function ShowNotification(text)
    SetNotificationTextEntry("STRING")
    AddTextComponentString(text)
    DrawNotification(false, false)
end

-- Function to show help text
function ShowHelpText(text)
    SetTextComponentFormat("STRING")
    AddTextComponentString(text)
    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
end

-- =====================================================
-- INITIALIZATION
-- =====================================================

Citizen.CreateThread(function()
    -- Wait for game to load
    while not NetworkIsPlayerActive(PlayerId()) do
        Citizen.Wait(100)
    end
    
    print('^2[Sapphire Gaming Commands]^0 Client-side handlers loaded successfully!')
end)
