Config = {}

-- Framework Settings
Config.Framework = 'esx' -- Options: 'esx', 'qb'

-- Notification Settings
Config.NotificationType = 'ox' -- Options: 'esx', 'ox', 'qb'

-- General Settings
Config.Locale = 'en'
Config.BankName = 'Sapphire Banking' -- Configurable bank name displayed in UI

-- Blip Configuration
Config.Blips = {
    enabled = true,
    sprite = 108, 
    color = 2, 
    scale = 0.8,
    name = "Bank",
    shortRange = true, 
    display = 4 
}

-- Banker Ped Configuration
Config.BankerPed = {
    enabled = false,
    model = "cs_bankman",
    scenario = "WORLD_HUMAN_CLIPBOARD", 
    invincible = true,
    freeze = true,
    blockEvents = true
}

-- Interaction Settings
Config.Interaction = {
    distance = 3.0, -- Distance to show interaction prompt
    key = 38, -- E key
    helpText = 'Press [E] to access banking',
    accessText = 'Banking system accessed!'
}

-- Banking Settings
Config.Banking = {
    enableATM = true,
    enableBankerPed = true,
    enableNUI = true,
    defaultAccount = 'bank',
    maxTransferAmount = 1000000,
    minTransferAmount = 1,
    transferFee = 0, -- Percentage fee for transfers (0 = no fee)
    enableTransactionHistory = true,
    maxHistoryEntries = 50
}

-- Discord Webhook Logging
Config.DiscordLogging = {
    enabled = true, -- Set to false to disable Discord logging
    webhook = "https://discord.com/api/webhooks/1396810743503912980/d9rJo9hp0zPTG6UeT9hJelb7SzR0t6G3m1fOJZgZuv7wEN6RaDLxCj8-rmn7aOP1n5rf", -- Your Discord webhook URL here
    botName = "Banking System",
    botAvatar = "https://i.ibb.co/Q7ddTp9S/images.png", -- Custom avatar URL
    color = {
        deposit = 3066993, 
        withdrawal = ********, 
        transfer = 3447003, 
        savings = ********, 
        admin = ********, 
        error = ******** 
    },
    logEvents = {
        deposits = true,
        withdrawals = true,
        transfers = true,
        savingsOperations = true,
        accountOperations = true, 
        historyClearing = true,
        pinOperations = true,
        adminActions = true
    }
}

-- ATM Configuration
Config.ATM = {
    enabled = true,
    models = {
        `prop_fleeca_atm`,
        `prop_atm_01`,
        `prop_atm_02`,
        `prop_atm_03`
    },
    interactionDistance = 2.0
}

-- Marker Configuration
Config.Markers = {
    enabled = true,
    type = 25, -- Marker type
    size = {x = 1.2, y = 1.2, z = 1.0}, -- Marker size
    color = {r = 38, g = 163, b = 240, a = 175}, -- Color #26a3f0 with 175 transparency
    drawDistance = 50.0 -- Distance to draw markers
}

-- Bank Locations
Config.BankLocations = {
    {
        coords = vector4(149.4113, -1042.0449, 29.3680, 342.9182) -- Legion Square
    },
    {
        coords = vector4(-1211.8585, -331.9854, 37.7809, 28.5983) -- Rockford Hills
    },
    {
        coords = vector4(-2961.0720, 483.1107, 15.6970, 88.1986) -- Great Ocean Highway
    },
    {
        coords = vector4(-112.2223, 6471.1128, 31.6267, 132.7517) -- Paleto Bay
    },
    {
        coords = vector4(313.8176, -280.5338, 54.1647, 339.1609) -- Pillbox Hill
    },
    {
        coords = vector4(-351.3247, -51.3466, 49.0365, 339.3305) -- Burton
    },
    {
        coords = vector4(1174.9718, 2708.2034, 38.0879, 178.2974) -- Sandy Shores
    },
    {
        coords = vector4(247.0348, 225.1851, 106.2875, 158.7528) -- Vinewood
    }
}
