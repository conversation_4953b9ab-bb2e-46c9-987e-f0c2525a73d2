# Sapphire Gaming Staff Panel

A comprehensive staff management system for Sapphire Gaming FiveM server with full 919admin integration.

## Features

### 🎯 **Core Features**
- **Modern Dark Theme UI** with Sapphire Gaming branding
- **Group-based Access Control** (god, leadership, cl, senioradmin, admin, mod)
- **Real-time Player Management** with detailed information
- **Advanced Names System** with health bars and staff hierarchy
- **Comprehensive Logging** of all staff actions with database storage
- **Server Management Tools** for admin+ users
- **Account ID Integration** with your custom account system

### 🚀 **919admin Integration Features**
- **NoClip System** with F2 key toggle and speed controls
- **Advanced Ban System** with database storage and auto-expiry
- **Warning System** with auto-ban after X warnings
- **Reports System** for player reports and staff management
- **Vehicle Spawning** with category selection and logging
- **Item Management** with give/take functionality
- **Money Management** (cash, bank, black money)
- **Spectate System** with full controls
- **Screenshot System** with Discord webhook integration
- **Advanced Player Actions** (freeze, revive, teleport, etc.)
- **Staff Notes System** for player documentation
- **Discord Webhook Integration** for all actions

### 👥 **Player Management**
- View all online players with detailed information
- Kick/Ban integration with txBans system
- Teleport to/bring players
- Clear player inventories (ox_inventory integration)
- Player information modal with job, money, and group details

### 🔧 **Staff Tools**
- **Names Display**: Toggle player names and health bars with staff hierarchy
- **Kill All/Heal All**: Server-wide player management (Admin+ only)
- **Time/Weather Control**: Change server time and weather (Admin+ only)
- **Real-time Staff List**: See online staff members with hierarchy stars

### 📊 **Dashboard**
- Server statistics (online players, staff count, uptime)
- Recent activity logs
- Clean, organized interface

## Installation

### 📋 **Prerequisites**
- ESX Legacy Framework
- MySQL database
- mysql-async resource
- Your custom account IDs system

### 🔧 **Installation Steps**

1. **Copy the resource** to your `[core]` folder:
   ```
   [core]/sg_staff/
   ```

2. **Import the database**:
   ```sql
   -- Execute the sg_staff.sql file in your database
   -- This creates all necessary tables for the enhanced system
   ```

3. **Configure Discord webhooks** (optional but recommended):
   ```lua
   -- Edit config.lua
   Config.Discord.webhook = 'YOUR_DISCORD_WEBHOOK_URL'
   Config.Screenshot.webhook = 'YOUR_SCREENSHOT_WEBHOOK_URL'
   ```

4. **Update your accounts table** (if needed):
   ```sql
   -- Ensure your accounts table has an 'account_id' column
   -- The system will automatically integrate with your custom account IDs
   ```

5. **Add to server.cfg**:
   ```
   ensure mysql-async
   ensure sg_staff
   ```

6. **Restart your server** or start the resource:
   ```
   restart sg_staff
   ```

### ⚙️ **Configuration**

Edit `config.lua` to customize:
- Staff group permissions and hierarchy
- Discord webhook settings
- Ban/warning system settings
- Vehicle spawn categories
- Money/item limits
- Screenshot settings

## Commands

### 🎮 **Player Commands**
- `/staff` - Open the staff panel (requires staff permissions)
- `/names` - Toggle names display (staff only)
- `/noclip` - Toggle noclip mode (staff only)
- `/closestaffpanel` - Emergency close command

### 🔧 **Admin Commands** (via panel)
- **Player Management**: Kick, ban, warn, spectate, screenshot
- **Money Management**: Give/take money (cash, bank, black money)
- **Vehicle Management**: Spawn vehicles, delete all vehicles
- **Item Management**: Give/remove items
- **Server Management**: Kill all, heal all, change time/weather
- **Report Management**: View, claim, close reports

## Permissions

### 🏆 **Staff Groups** (in order of hierarchy):

#### **1. God (★G) - Power Level 100**
- **Full access to everything**
- Resource control, mass entity deletion
- Character deletion, view all characters
- All 919admin features enabled

#### **2. Leadership (★1) - Power Level 90**
- **Nearly full access**
- Cannot control resources or mass delete entities
- Cannot clear reports/admin chat
- All other features enabled
- **Money management permissions**

#### **3. CL (★2) - Power Level 80**
- **High-level management**
- Cannot control resources, mass delete, or clear data
- Cannot use free aim mode
- All player management features
- **Money management permissions**

#### **4. Senior Admin (★3) - Power Level 75**
- **Enhanced admin privileges**
- Advanced player management tools
- Character viewing capabilities
- **No money management permissions**
- Cannot delete characters or control resources

#### **5. Admin (★4) - Power Level 70**
- **Standard admin features**
- Cannot delete characters or unban players
- Limited to player management and basic admin tools
- Cannot control server resources
- **No money management permissions**

#### **6. Mod (★5) - Power Level 50**
- **Basic moderation**
- Cannot ban players or manage money/items
- Cannot spawn vehicles or change server settings
- Limited to kick, warn, spectate, and basic actions
- **No money management permissions**

## Configuration

Edit `config.lua` to customize:
- Staff group permissions
- Names system settings
- UI refresh intervals
- Ban system integration
- Logging preferences

## Dependencies

### 📦 **Required**
- **es_extended** (ESX Legacy Framework)
- **mysql-async** (Database operations)
- Your custom account IDs system

### 🔧 **Optional**
- **txBans** (enhanced ban system integration)
- **ox_inventory** (inventory clearing)
- **screenshot-basic** (screenshot functionality)

## Database Integration

### 🗄️ **TX Player ID System**
The panel integrates with the TX default player-data system by:
- Using TX default player IDs from the player-data resource
- Storing all logs, bans, warnings with TX player IDs instead of account IDs
- Maintaining compatibility with the CFX default player identification system

### 📊 **Database Tables Created**
- `sg_staff_logs` - All staff actions
- `sg_staff_bans` - Enhanced ban system
- `sg_staff_warnings` - Warning system
- `sg_staff_reports` - Player reports
- `sg_staff_chat` - Admin chat logs
- `sg_staff_notes` - Player notes
- `sg_staff_sessions` - Staff activity tracking
- `sg_staff_vehicle_spawns` - Vehicle spawn logs
- `sg_staff_money_logs` - Money transaction logs
- `sg_staff_item_logs` - Item transaction logs

## Discord Integration

### 🔗 **Webhook Features**
- **Ban/Unban notifications** with player details
- **Warning notifications** with warning counts
- **Money transaction logs** with amounts and types
- **Vehicle spawn notifications** with models
- **Item transaction logs** with quantities
- **Screenshot uploads** with staff information
- **Report notifications** for new player reports

## Advanced Features

### 🎯 **919admin Integration**
All original 919admin features have been integrated:
- **NoClip system** with speed controls
- **Advanced spectate** with player switching
- **Screenshot system** with Discord upload
- **Entity management** (delete vehicles, peds, objects)
- **Resource control** (god/owner only)
- **View distance control**
- **Free aim mode toggle**
- **Mass entity deletion**

### 🔒 **Security Features**
- **Permission-based access** to all functions
- **Account ID tracking** for all actions
- **Comprehensive logging** of all staff activities
- **Ban evasion protection** with multiple identifier checks
- **Auto-ban system** for excessive warnings

## Branding

This version is specifically branded for **Sapphire Gaming** with:
- **Purple color scheme** (#8a2be2, #9932cc)
- **Sapphire Gaming logo** and branding elements
- **Custom announcement prefix** `[SAPPHIRE GAMING STAFF]`
- **Server-specific styling** and themes
- **Professional dark theme** interface

## Support & Updates

### 📞 **Support**
For support with this staff panel, contact the Sapphire Gaming development team.

### 🔄 **Updates**
This system includes all features from:
- Original ESX staff panel
- 919admin complete feature set
- Custom account ID integration
- Enhanced database logging
- Discord webhook integration

## Version

**Version 3.0.0** - Sapphire Gaming Edition with Full 919admin Integration

### 📋 **Changelog**
- ✅ Full 919admin feature integration
- ✅ Account ID system compatibility
- ✅ Enhanced database logging
- ✅ Discord webhook integration
- ✅ Advanced ban/warning system
- ✅ Vehicle and item management
- ✅ Screenshot and spectate systems
- ✅ NoClip and advanced admin tools
- ✅ Reports and notes system
- ✅ Comprehensive permission system

---

*Developed for Sapphire Gaming FiveM Server - Complete Admin Solution*
