// Enhanced Sapphire Gaming Chat Configuration
window.CONFIG = {
  defaultTemplateId: 'default', //This is the default template for 2 args
  defaultAltTemplateId: 'defaultAlt', //This one for 1 arg
  templates: { //You can add static templates here
    'default': '<span style="color: #ffffff; font-weight: bold;">{0}</span>: <span style="color: #ffffff;">{1}</span>',
    'defaultAlt': '<span style="color: #ffffff; font-weight: bold;">{0}</span>',
    'print': '<pre style="color: #ffffff; font-weight: bold;">{0}</pre>',
    'example:important': '<h1>^2{0}</h1>',

    // Staff chat - all red and bold
    'staff': '<span style="color: #ff0000; font-weight: bold;">[STAFF] {0}: {1}</span>',
    'admin': '<span style="color: #ff0000; font-weight: bold;">[ADMIN] {0}: {1}</span>',
    
    // Announcements - red system message with white body, bold
    'announcement': '<span style="color: #ff0000; font-weight: bold;">[ANNOUNCEMENT]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    'system': '<span style="color: #ff0000; font-weight: bold;">SYSTEM:</span> <span style="color: white; font-weight: bold;">{0}</span>',
    
    // Enhanced roleplay commands - bold for cleaner look
    'me': '<span style="color: #7bed9f; font-style: italic; font-weight: bold;">* {0} {1}</span>',
    'do': '<span style="color: #ff9ff3; font-style: italic; font-weight: bold;">* {1} (( {0} ))</span>',
    
    // Additional message types
    'warning': '<span style="color: #f39c12; font-weight: bold;">[WARNING]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    'error': '<span style="color: #e74c3c; font-weight: bold;">[ERROR]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    'success': '<span style="color: #27ae60; font-weight: bold;">[SUCCESS]</span> <span style="color: white; font-weight: bold;">{0}</span>',
    'info': '<span style="color: #3498db; font-weight: bold;">[INFO]</span> <span style="color: white; font-weight: bold;">{0}</span>'
  },
  fadeTimeout: 7000,
  suggestionLimit: 5,
  style: {
    background: 'rgba(52, 73, 94, 0.8)',
    width: '38%',
    height: '22%',
  }
};
