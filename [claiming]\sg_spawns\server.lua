-- server.lua

ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Callback to get player's last position
ESX.RegisterServerCallback('sg_spawns:getLastPosition', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then 
        cb(nil)
        return 
    end

    local identifier = xPlayer.identifier

    MySQL.Async.fetchScalar('SELECT position FROM users WHERE identifier = @identifier', {
        ['@identifier'] = identifier
    }, function(position)
        if position then
            local pos = json.decode(position)
            if pos and pos.x and pos.y and pos.z then
                cb(vector3(pos.x, pos.y, pos.z))
            else
                cb(nil)
            end
        else
            cb(nil)
        end
    end)
end)

-- Save player position when they disconnect
AddEventHandler('esx:playerDropped', function(playerId, reason)
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if not xPlayer then return end

    local ped = GetPlayerPed(playerId)
    local coords = GetEntityCoords(ped)
    
    MySQL.Async.execute('UPDATE users SET position = @position WHERE identifier = @identifier', {
        ['@position'] = json.encode({
            x = coords.x,
            y = coords.y,
            z = coords.z
        }),
        ['@identifier'] = xPlayer.identifier
    })
end)

-- Periodically save player positions (every 5 minutes)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(300000) -- 5 minutes
        
        local xPlayers = ESX.GetPlayers()
        for i = 1, #xPlayers do
            local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
            if xPlayer then
                local ped = GetPlayerPed(xPlayer.source)
                local coords = GetEntityCoords(ped)
                
                MySQL.Async.execute('UPDATE users SET position = @position WHERE identifier = @identifier', {
                    ['@position'] = json.encode({
                        x = coords.x,
                        y = coords.y,
                        z = coords.z
                    }),
                    ['@identifier'] = xPlayer.identifier
                })
            end
        end
    end
end)
