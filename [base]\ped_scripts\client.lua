-- Infinite Stamina
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        -- reset the local player’s stamina to full every frame
        ResetPlayerStamina(PlayerId())
    end
end)



---crouch
-- client.lua

local isCrouched = false
local crouchSets = {
  move   = "move_ped_crouched",
  strafe = "move_ped_crouched_strafing",
  weapon = "move_ped_crouched",
}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    local ped    = PlayerPedId()
    local aiming = IsPlayerFreeAiming(PlayerId())
    local shooting = IsPedShooting(ped)

    -- Always block GTA’s default duck
    DisableControlAction(0, 36, true)  -- INPUT_DUCK (CTRL)

    -- If you start aiming, force-stand
    if aiming and isCrouched then
      isCrouched = false
      ResetPedMovementClipset(ped, 0.1)
      ResetPedStrafeClipset(ped)
      ResetPedWeaponMovementClipset(ped)

    -- Toggle crouch on CTRL only when NOT shooting and on foot
    elseif not aiming and not IsPedInAnyVehicle(ped, false) then
      if IsDisabledControlJustPressed(0, 36) and not shooting then  -- LEFT CTRL
        isCrouched = not isCrouched

        if isCrouched then
          -- load animation sets
          for _, set in pairs(crouchSets) do
            RequestAnimSet(set)
          end
          while not HasAnimSetLoaded(crouchSets.move) do
            Citizen.Wait(0)
          end
          -- apply crouch
          SetPedMovementClipset(ped, crouchSets.move, 0.75)
          SetPedStrafeClipset(ped,   crouchSets.strafe)
          SetPedWeaponMovementClipset(ped, crouchSets.weapon)
        else
          -- reset to stand
          ResetPedMovementClipset(ped,        0.25)
          ResetPedStrafeClipset(ped)
          ResetPedWeaponMovementClipset(ped)
        end
      end
    end

    -- If crouched, block shooting
    if isCrouched then
      DisableControlAction(0, 24, true)  -- INPUT_ATTACK (left click)
      DisableControlAction(0, 257, true) -- INPUT_ATTACK2
    end
  end
end)

SetCanAttackFriendly(PlayerPedId(), true, false)
NetworkSetFriendlyFireOption(true)


-- client.lua
Citizen.CreateThread(function()
    -- wait a tick for the ped to spawn
    Citizen.Wait(1000)

    local ped = PlayerPedId()

    -- Disable the automatic “fall-off” tests (so you don’t catch your foot)
    SetPedConfigFlag(ped, 54, true)    -- PED_FLAG_DISSABLE_AUTO_FALL_OFF_TESTS :contentReference[oaicite:0]{index=0}

    -- Prevent ragdolling from the fall itself
    SetPedConfigFlag(ped, 164, true)   -- PED_FLAG_DONT_ACTIVATE_RAGDOLL_FROM_FALLING :contentReference[oaicite:1]{index=1}

    -- Stop the game from detecting stairs/slopes as “trippery”
    SetPedConfigFlag(ped, 147, false)  -- PED_FLAG_STAIRS_DETECTED :contentReference[oaicite:2]{index=2}
    SetPedConfigFlag(ped, 148, false)  -- PED_FLAG_SLOPE_DETECTED :contentReference[oaicite:3]{index=3}
end)

-- (Re-apply on ped respawn, if needed)
AddEventHandler('playerSpawned', function()
    Citizen.Wait(1000)
    local ped = PlayerPedId()

    SetPedConfigFlag(ped, 54, true)
    SetPedConfigFlag(ped, 164, true)
    SetPedConfigFlag(ped, 147, false)
    SetPedConfigFlag(ped, 148, false)
end)

