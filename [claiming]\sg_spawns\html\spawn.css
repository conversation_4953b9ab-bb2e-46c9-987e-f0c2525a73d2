body { 
  margin: 0; 
  padding: 0; 
  overflow: hidden; 
}

#spawn-menu {
  position: absolute;
  top: 12%;            /* move up slightly */
  right: 2%;           /* shift further right */
  width: 32%;          /* a touch wider */
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  padding: 20px;       /* more padding */
  font-family: Arial, sans-serif;
  color: #eee;
}

/* HEADER */
#header {
  display: flex;
  align-items: center;
  margin-bottom: 20px; /* extra space below header */
}
#server-logo {
  width: 80px;         /* larger logo */
  height: 80px;
  margin-right: 15px;
  object-fit: contain;
}
#player-name {
  font-size: 28px;     /* bigger name text */
  font-weight: bold;
  color: #fff;
}

/* OPTIONS LIST */
#options {
  list-style: none;
  margin: 0;
  padding: 0;
}
.option-item {
  position: relative;
  padding: 16px 24px;  /* more padding */
  margin: 10px 0;      /* more separation */
  font-size: 20px;     /* larger text */
  text-transform: uppercase;
  border-radius: 25px;
  cursor: pointer;
  user-select: none;
  background: rgba(0, 0, 0, 0.5);
  transition: transform .1s, background .1s, font-weight .1s;
  font-weight: 600;
}

/* hover effect on Last & Spawn options only */
.btn-last:hover,
.btn-spawn:hover {
  transform: scale(1.02);
  background: rgba(0, 0, 0, 0.7);
}

/* CONFIRM BUTTON */
.btn-confirm.disabled {
  background: rgba(100, 100, 100, 0.5);
  color: #ccc;
  cursor: not-allowed;
}
.btn-confirm.enabled {
  background: #4CAF50;
  color: #fff;
  cursor: pointer;
}
.btn-confirm.enabled:hover {
  /* no extra hover effect, stays solid green */
  transform: none;
}

/* SELECTED LOCATION PILL */
.option-item.selected {
  background: #2979FF !important;
  color: #fff !important;
  font-weight: 700;
}
