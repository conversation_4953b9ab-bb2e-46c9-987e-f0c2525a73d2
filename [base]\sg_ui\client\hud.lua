local Bar = Config.Bar
local Color = Config.Colors
local HideHU = Config.HideDefault
local HUD_HEALTH_ICON = 3

-- Open the HUD setup UI
RegisterCommand("hud", function()
  SetNuiFocus(true, true)               -- show mouse cursor
  SendNUIMessage({ action = "toggleHUDMenu" })
end, false)

-- Close the UI and release mouse
RegisterNUICallback("exitHUD", function(data, cb)
  SetNuiFocus(false, false)
  cb("ok")
end)

-- Send health and armor updates to the NUI
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(250)
    local ped = PlayerPedId()
    if HideHU then
      HideHudComponentThisFrame(HUD_HEALTH_ICON)
    end

    -- Health calculation
    local health    = GetEntityHealth(ped) - 100
    local maxHealth = GetEntityMaxHealth(ped) - 100
    local pct       = math.max(0.0, math.min(1.0, health / maxHealth))
    local displayPct = math.floor(pct * 100)

    -- Armor calculation
    local armor = GetPedArmour(ped)
    local armorPct = math.max(0, math.min(100, armor))

    local status = "Healthy"
    if displayPct <= 0  then status = "Click [E] to Respawn"
    elseif displayPct <= 10 then status = "Critical"
    elseif displayPct <= 30 then status = "Injured"
    elseif displayPct <= 55 then status = "Bleeding"
    elseif displayPct <= 70 then status = "Hurt"
    end

    SendNUIMessage({
      action  = "updateHealth",
      percent = displayPct,
      status  = status,
      armor   = armorPct
    })
  end
end)

SetCanAttackFriendly(PlayerPedId(), true, false)
NetworkSetFriendlyFireOption(false)


--==================================================================
-- client.lua
-- Voice‐Mode Boxes (cycle modes with backtick), drawn vertically:
--     → Whisper  (1 box)
--     → Normal   (2 boxes)
--     → Shout    (3 boxes)
--==================================================================

-- CONFIGURATION (tweak these to reposition/resize)
local squareSize   = 0.006    -- width & height of each box (relative screen coords)
local padding      = 0.003    -- space between each box
-- Position of the BOTTOM box (just left of your minimap):
local baseX        = 0.0095  -- X coordinate for all boxes
local baseY        = 0.991    -- Y coordinate for the lowest box (near minimap)

-- Colors (RGBA)
local greyColor    = {  59,  59,  59,  175 }  -- dark grey (silent)
local blueColor    = {   4, 112, 198,  255 }  -- light blue (speaking)

-- STATE
local mode         = 2       -- 1=Whisper, 2=Normal, 3=Shout
local isTalking    = false   -- true when mic (push-to-talk) is active

-- Control index for the backtick () key in FiveM
local BACKTICK_CONTROL = 243

--==================================================================
-- 1) TRACK WHETHER THE PLAYER IS TALKING
--==================================================================
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(50)
    isTalking = NetworkIsPlayerTalking(PlayerId())
  end
end)

--==================================================================
-- 2) CYCLE THROUGH MODES WHEN BACKTICK IS PRESSED
--==================================================================
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if IsControlJustReleased(0, BACKTICK_CONTROL) then
      mode = mode + 1
      if mode > 3 then
        mode = 1
      end
    end
  end
end)

--==================================================================
-- 3) DRAW THE BOXES IN A VERTICAL COLUMN
--==================================================================
Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)
    if NetworkIsPlayerActive(PlayerId()) then
      -- Draw 'mode' number of boxes, stacked upward
      for i = 1, mode do
        -- X stays constant (baseX), Y moves up by (squareSize + padding) each step
        local thisX = baseX
        local thisY = baseY - ((i - 1) * (squareSize + padding))

        -- Choose color: grey if silent, blue if talking
        local c = isTalking and blueColor or greyColor

        DrawRect(
          thisX, thisY,
          squareSize, squareSize,
          c[1], c[2], c[3], c[4]
        )
      end
    end
  end
end)


SetCanAttackFriendly(PlayerPedId(), true, false)
NetworkSetFriendlyFireOption(false)

Citizen.CreateThread(function()
    -- quick big-map toggle to initialize radar properly
    SetRadarBigmapEnabled(true, false)
    Wait(0)
    SetRadarBigmapEnabled(false, false)

    while true do
        -- toggle HUD on/off with CTRL + H (control 19 + 74)
        if IsControlPressed(1, 19) then
            if IsControlJustPressed(1, 74) then
                hudHidden = not hudHidden
            end
        end

        -- hide cash, messages, vehicle name/class, area/street name, help text
        HideHudComponentThisFrame(3)  -- MP_CASH
        HideHudComponentThisFrame(4)  -- MP_MESSAGE
        HideHudComponentThisFrame(6)  -- AREA_NAME
        HideHudComponentThisFrame(7)  -- VEHICLE_CLASS
        HideHudComponentThisFrame(8)  -- STREET_NAME
        HideHudComponentThisFrame(9)  -- HELP_TEXT

        Wait(1)
    end
end)


