print('^3[Sapphire Gaming Staff Panel]^0 Starting resource...')

-- Global ESX variable
ESX = nil

-- Server data storage
local playersWithNames = {}
local staffLogs = {}
local serverStartTime = os.time()
local adminPanel = {
    open = false,
    reports = {},
    adminChat = {},
    playerList = {},
    serverInfo = {}
}

-- Performance optimization: <PERSON><PERSON> frequently accessed data
local playerDataCache = {}
local staffMembersCache = {}
local lastCacheUpdate = 0
local CACHE_DURATION = 30000 -- 30 seconds

-- Function to clear cache when player data changes
local function clearPlayerCache()
    playerDataCache = {}
    staffMembersCache = {}
    lastCacheUpdate = 0
end

-- Allowed groups for staff panel (Enhanced with god group)
local allowedGroups = {
    ['god'] = true,
    ['leadership'] = true,
    ['cl'] = true,
    ['senioradmin'] = true,
    ['admin'] = true,
    ['mod'] = true
}

-- Admin-only groups for server management panel
local adminOnlyGroups = {
    ['god'] = true,
    ['leadership'] = true,
    ['cl'] = true,
    ['senioradmin'] = true,
    ['admin'] = true
}

-- Use staff groups from config
local staffGroups = Config.StaffGroups

-- Database initialization
CreateThread(function()
    Wait(1000)

    -- Initialize database tables
    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `sg_staff_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `staff_account_id` int(11) NOT NULL,
            `staff_name` varchar(100) NOT NULL,
            `staff_identifier` varchar(60) NOT NULL,
            `action_type` varchar(50) NOT NULL,
            `target_account_id` int(11) DEFAULT NULL,
            `target_name` varchar(100) DEFAULT NULL,
            `target_identifier` varchar(60) DEFAULT NULL,
            `details` longtext DEFAULT NULL,
            `reason` text DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            INDEX `idx_staff_account_id` (`staff_account_id`),
            INDEX `idx_target_account_id` (`target_account_id`),
            INDEX `idx_action_type` (`action_type`),
            INDEX `idx_timestamp` (`timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ]])

    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `sg_staff_bans` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` int(11) NOT NULL,
            `name` varchar(100) NOT NULL,
            `identifier` varchar(60) NOT NULL,
            `license` varchar(50) DEFAULT NULL,
            `discord` varchar(50) DEFAULT NULL,
            `ip` varchar(50) DEFAULT NULL,
            `reason` text NOT NULL,
            `banned_by_player_id` int(11) NOT NULL,
            `banned_by_name` varchar(100) NOT NULL,
            `banned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `expires_at` timestamp NULL DEFAULT NULL,
            `is_permanent` tinyint(1) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `unbanned_by_player_id` int(11) DEFAULT NULL,
            `unbanned_by_name` varchar(100) DEFAULT NULL,
            `unbanned_at` timestamp NULL DEFAULT NULL,
            `unban_reason` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            INDEX `idx_player_id` (`player_id`),
            INDEX `idx_identifier` (`identifier`),
            INDEX `idx_license` (`license`),
            INDEX `idx_is_active` (`is_active`),
            INDEX `idx_expires_at` (`expires_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ]])

    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `sg_staff_warnings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `account_id` int(11) NOT NULL,
            `name` varchar(100) NOT NULL,
            `identifier` varchar(60) NOT NULL,
            `reason` text NOT NULL,
            `warned_by_account_id` int(11) NOT NULL,
            `warned_by_name` varchar(100) NOT NULL,
            `warned_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `is_active` tinyint(1) DEFAULT 1,
            PRIMARY KEY (`id`),
            INDEX `idx_account_id` (`account_id`),
            INDEX `idx_identifier` (`identifier`),
            INDEX `idx_warned_at` (`warned_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ]])

    MySQL.query([[
        CREATE TABLE IF NOT EXISTS `sg_staff_reports` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `reporter_account_id` int(11) NOT NULL,
            `reporter_name` varchar(100) NOT NULL,
            `reporter_identifier` varchar(60) NOT NULL,
            `subject` varchar(255) NOT NULL,
            `message` text NOT NULL,
            `status` enum('open','claimed','closed') DEFAULT 'open',
            `claimed_by_account_id` int(11) DEFAULT NULL,
            `claimed_by_name` varchar(100) DEFAULT NULL,
            `claimed_at` timestamp NULL DEFAULT NULL,
            `closed_by_account_id` int(11) DEFAULT NULL,
            `closed_by_name` varchar(100) DEFAULT NULL,
            `closed_at` timestamp NULL DEFAULT NULL,
            `close_reason` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            INDEX `idx_reporter_account_id` (`reporter_account_id`),
            INDEX `idx_status` (`status`),
            INDEX `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ]])

    print('^2[Sapphire Gaming Staff Panel]^0 Database tables initialized')
end)

-- Safely get player from ESX
local function safeGetPlayer(source)
    if not ESX then
        return nil
    end

    local success, xPlayer = pcall(function()
        return ESX.GetPlayerFromId(source)
    end)

    return success and xPlayer or nil
end

-- Get player TX default ID (replaces account ID system)
local function getPlayerTxId(source)
    local xPlayer = safeGetPlayer(source)
    if not xPlayer then return nil end

    -- Get TX default player ID using the player-data resource
    if GetResourceState('player-data') == 'started' then
        local playerId = exports['player-data']:getPlayerId(source)
        if playerId then
            return playerId
        end
    end

    -- Fallback to source if player-data is not available
    return source
end

-- Get player data with TX player ID
local function getPlayerData(source)
    local xPlayer = safeGetPlayer(source)
    if not xPlayer then return nil end

    local playerId = getPlayerTxId(source)
    if not playerId then return nil end

    return {
        source = source,
        playerId = playerId, -- Changed from accountId to playerId
        name = xPlayer.getName(),
        identifier = xPlayer.getIdentifier(),
        group = xPlayer.getGroup(),
        job = xPlayer.getJob().name,
        jobGrade = xPlayer.getJob().grade,
        money = xPlayer.getMoney(),
        bank = xPlayer.getAccount('bank').money,
        license = xPlayer.getIdentifier('license'),
        discord = xPlayer.getIdentifier('discord'),
        ip = GetPlayerEndpoint(source)
    }
end

-- Enhanced logging with TX player IDs
local function addLogWithPlayerId(actionType, staffSource, targetSource, details, reason)
    local staffData = getPlayerData(staffSource)
    local targetData = targetSource and getPlayerData(targetSource) or nil

    if not staffData then return end

    local logData = {
        staff_player_id = staffData.playerId, -- Changed from staff_account_id
        staff_name = staffData.name,
        staff_identifier = staffData.identifier,
        action_type = actionType,
        target_player_id = targetData and targetData.playerId or nil, -- Changed from target_account_id
        target_name = targetData and targetData.name or nil,
        target_identifier = targetData and targetData.identifier or nil,
        details = details,
        reason = reason
    }

    MySQL.insert('INSERT INTO sg_staff_logs (staff_account_id, staff_name, staff_identifier, action_type, target_account_id, target_name, target_identifier, details, reason) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        logData.staff_account_id,
        logData.staff_name,
        logData.staff_identifier,
        logData.action_type,
        logData.target_account_id,
        logData.target_name,
        logData.target_identifier,
        logData.details,
        logData.reason
    })

    -- Also add to memory for quick access
    table.insert(staffLogs, 1, {
        id = #staffLogs + 1,
        type = actionType,
        message = details or string.format('%s performed %s on %s', staffData.name, actionType, targetData and targetData.name or 'server'),
        staff = staffData.name,
        target = targetData and targetData.name or nil,
        timestamp = os.date('%Y-%m-%d %H:%M:%S'),
        time = os.time()
    })

    -- Keep only max entries (100)
    if #staffLogs > 100 then
        table.remove(staffLogs, #staffLogs)
    end

    print(string.format('[SAPPHIRE GAMING STAFF LOG] %s: %s', actionType, details or 'No details'))
end

-- Discord webhook function
local function sendDiscordLog(title, description, color, fields)
    if not Config.Discord.enabled or Config.Discord.webhook == 'CHANGEME' then
        return
    end

    local embed = {
        {
            title = title,
            description = description,
            color = color or Config.Discord.colors.general,
            fields = fields or {},
            timestamp = os.date('!%Y-%m-%dT%H:%M:%SZ'),
            footer = {
                text = 'Sapphire Gaming Staff Panel',
                icon_url = Config.Discord.botAvatar
            }
        }
    }

    PerformHttpRequest(Config.Discord.webhook, function(err, text, headers) end, 'POST', json.encode({
        username = Config.Discord.botName,
        avatar_url = Config.Discord.botAvatar,
        embeds = embed
    }), { ['Content-Type'] = 'application/json' })
end

-- Check if player has permission to use staff panel
local function hasStaffPermission(xPlayer)
    if not xPlayer then return false end
    local success, playerGroup = pcall(function()
        return xPlayer.getGroup()
    end)
    return success and allowedGroups[playerGroup] == true
end

-- Check if player has admin permission for server management
local function hasAdminPermission(xPlayer)
    if not xPlayer then return false end
    local success, playerGroup = pcall(function()
        return xPlayer.getGroup()
    end)
    return success and adminOnlyGroups[playerGroup] == true
end

-- Check if player has specific permission
local function hasPermission(xPlayer, permission)
    if not xPlayer then return false end
    local success, playerGroup = pcall(function()
        return xPlayer.getGroup()
    end)
    if not success then return false end
    local groupConfig = staffGroups[playerGroup]
    return groupConfig and groupConfig[permission] == true
end

-- Add log entry
local function addLog(type, message, staffName, targetPlayer)
    local logEntry = {
        id = #staffLogs + 1,
        type = type,
        message = message,
        staff = staffName,
        target = targetPlayer,
        timestamp = os.date('%Y-%m-%d %H:%M:%S'),
        time = os.time()
    }

    table.insert(staffLogs, 1, logEntry) -- Insert at beginning

    -- Keep only max entries (100)
    if #staffLogs > 100 then
        table.remove(staffLogs, #staffLogs)
    end

    print(string.format('[SAPPHIRE GAMING STAFF LOG] %s: %s', type, message))
end

-- Simple test command first (no ESX dependency)
RegisterCommand('stafftest', function(source, args, rawCommand)
    print('^2[Sapphire Gaming Staff Panel]^0 Test command executed by player ' .. source)
    TriggerClientEvent('sg_staff:openPanel', source)
end, false)

print('^2[Sapphire Gaming Staff Panel]^0 Test command registered!')

-- Initialize ESX safely
CreateThread(function()
    local attempts = 0
    while not ESX and attempts < 30 do
        attempts = attempts + 1
        local success, result = pcall(function()
            return exports["es_extended"]:getSharedObject()
        end)

        if success and result then
            ESX = result
            print('^2[Sapphire Gaming Staff Panel]^0 ESX loaded successfully!')
            break
        else
            print(string.format('^1[Sapphire Gaming Staff Panel]^0 ESX not ready, attempt %d/30', attempts))
            Wait(1000)
        end
    end

    if not ESX then
        print('^1[Sapphire Gaming Staff Panel]^0 Failed to load ESX after 30 attempts!')
        return
    end

    -- Register main staff command
    ESX.RegisterCommand('staff', 'mod', function(xPlayer, args, showError)
        print(string.format('^3[Sapphire Gaming Staff Panel]^0 /staff command used by %s (ID: %d)', xPlayer.getName(), xPlayer.source))
        TriggerClientEvent('sg_staff:openPanel', xPlayer.source)
    end, false, {help = 'Open Sapphire Gaming Staff Panel'})

    -- Register time command for Senior Admin+
    ESX.RegisterCommand('time', 'senioradmin', function(xPlayer, args, showError)
        if #args < 1 then
            showError('Usage: /time <hour> [minute] - Example: /time 12 30 or /time 18')
            return
        end

        local hour = tonumber(args[1])
        local minute = tonumber(args[2]) or 0

        -- Validate time
        if not hour or hour < 0 or hour > 23 then
            showError('Invalid hour! Must be between 0-23')
            return
        end

        if minute < 0 or minute > 59 then
            showError('Invalid minute! Must be between 0-59')
            return
        end

        -- Check if player has permission
        if not hasPermission(xPlayer, 'canTime') then
            showError('You do not have permission to change server time')
            return
        end

        -- Set time for all players
        TriggerClientEvent('sg_staff:syncTime', -1, hour, minute)

        -- Log the action
        addLogWithPlayerId('TIME_CHANGE', xPlayer.source, nil, string.format('%s changed server time to %02d:%02d', xPlayer.getName(), hour, minute), nil)

        -- Notify the staff member
        TriggerClientEvent('esx:showNotification', xPlayer.source, string.format('Server time changed to %02d:%02d', hour, minute))

        print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s changed server time to %02d:%02d', xPlayer.getName(), hour, minute))
    end, false, {help = 'Change server time - Usage: /time <hour> [minute]'})

    -- Register weather command for Senior Admin+
    ESX.RegisterCommand('weather', 'senioradmin', function(xPlayer, args, showError)
        if #args < 1 then
            showError('Usage: /weather <type> - Available: clear, extrasunny, clouds, overcast, rain, thunder, snow, blizzard, snowlight, xmas, halloween')
            return
        end

        local weather = string.upper(args[1])
        local validWeathers = {
            'CLEAR', 'EXTRASUNNY', 'CLOUDS', 'OVERCAST', 'RAIN', 'THUNDER', 'SNOW', 'BLIZZARD', 'SNOWLIGHT', 'XMAS', 'HALLOWEEN'
        }

        -- Check if weather type is valid
        local isValid = false
        for _, validWeather in ipairs(validWeathers) do
            if weather == validWeather then
                isValid = true
                break
            end
        end

        if not isValid then
            showError('Invalid weather type! Available: clear, extrasunny, clouds, overcast, rain, thunder, snow, blizzard, snowlight, xmas, halloween')
            return
        end

        -- Check if player has permission
        if not hasPermission(xPlayer, 'canWeather') then
            showError('You do not have permission to change server weather')
            return
        end

        -- Set weather for all players
        TriggerClientEvent('sg_staff:syncWeather', -1, weather)

        -- Log the action
        addLogWithPlayerId('WEATHER_CHANGE', xPlayer.source, nil, string.format('%s changed server weather to %s', xPlayer.getName(), weather), nil)

        -- Notify the staff member
        TriggerClientEvent('esx:showNotification', xPlayer.source, string.format('Server weather changed to %s', weather))

        print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s changed server weather to %s', xPlayer.getName(), weather))
    end, false, {help = 'Change server weather - Usage: /weather <type>'})

    print('^2[Sapphire Gaming Staff Panel]^0 Main /staff command registered!')
    print('^2[Sapphire Gaming Staff Panel]^0 /time command registered!')
    print('^2[Sapphire Gaming Staff Panel]^0 /weather command registered!')
end)

-- Get player data
RegisterServerEvent('sg_staff:getPlayerData')
AddEventHandler('sg_staff:getPlayerData', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer then
        print('^1[Sapphire Gaming Staff Panel]^0 Failed to get player data for source: ' .. source)
        return
    end

    if not hasStaffPermission(xPlayer) then
        print('^1[Sapphire Gaming Staff Panel]^0 Player ' .. source .. ' does not have staff permission')
        return
    end

    local playerData = {
        source = source,
        name = xPlayer.getName(),
        group = xPlayer.getGroup(),
        identifier = xPlayer.getIdentifier(),
        job = xPlayer.getJob().name,
        jobGrade = xPlayer.getJob().grade,
        money = xPlayer.getMoney(),
        bank = xPlayer.getAccount('bank').money
    }

    TriggerClientEvent('sg_staff:receivePlayerData', source, playerData)
end)

-- Get online players
RegisterServerEvent('sg_staff:getOnlinePlayers')
AddEventHandler('sg_staff:getOnlinePlayers', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasStaffPermission(xPlayer) then
        return
    end

    -- Check cache first
    local currentTime = GetGameTimer()
    if currentTime - lastCacheUpdate < CACHE_DURATION and playerDataCache[1] then
        TriggerClientEvent('sg_staff:receiveOnlinePlayers', source, playerDataCache)
        return
    end

    local players = {}
    local xPlayers = ESX.GetPlayers()

    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer then
            local playerData = {
                source = targetPlayer.source,
                name = targetPlayer.getName(),
                group = targetPlayer.getGroup(),
                identifier = targetPlayer.getIdentifier(),
                job = targetPlayer.getJob().name,
                jobGrade = targetPlayer.getJob().grade,
                money = targetPlayer.getMoney(),
                bank = targetPlayer.getAccount('bank').money,
                hasNamesEnabled = playersWithNames[targetPlayer.source] == true
            }

            -- Add level information for staff members
            if staffGroups[playerData.group] then
                playerData.level = staffGroups[playerData.group].level
            end

            table.insert(players, playerData)
        end
    end

    -- Update cache
    playerDataCache = players
    lastCacheUpdate = currentTime

    TriggerClientEvent('sg_staff:receiveOnlinePlayers', source, players)

    -- Also send server stats
    local stats = {
        onlinePlayers = #xPlayers,
        staffOnline = 0,
        uptime = os.time() - serverStartTime,
        recentReports = 0
    }

    -- Count staff members
    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer and allowedGroups[targetPlayer.getGroup()] then
            stats.staffOnline = stats.staffOnline + 1
        end
    end

    TriggerClientEvent('sg_staff:receiveServerStats', source, stats)
end)

-- Get staff members
RegisterServerEvent('sg_staff:getStaffMembers')
AddEventHandler('sg_staff:getStaffMembers', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasStaffPermission(xPlayer) then
        return
    end

    local staffMembers = {}
    local xPlayers = ESX.GetPlayers()

    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer and allowedGroups[targetPlayer.getGroup()] then
            local staffData = {
                source = targetPlayer.source,
                name = targetPlayer.getName(),
                group = targetPlayer.getGroup(),
                level = staffGroups[targetPlayer.getGroup()].level,
                hasNamesEnabled = playersWithNames[targetPlayer.source] == true
            }
            table.insert(staffMembers, staffData)
        end
    end

    TriggerClientEvent('sg_staff:receiveStaffMembers', source, staffMembers)
end)

-- Get players with names enabled
RegisterServerEvent('sg_staff:getPlayersWithNames')
AddEventHandler('sg_staff:getPlayersWithNames', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasStaffPermission(xPlayer) then
        return
    end

    local playersWithNamesArray = {}
    for playerId, _ in pairs(playersWithNames) do
        table.insert(playersWithNamesArray, playerId)
    end

    TriggerClientEvent('sg_staff:receivePlayersWithNames', source, playersWithNamesArray)
end)

-- Get logs
RegisterServerEvent('sg_staff:getLogs')
AddEventHandler('sg_staff:getLogs', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasStaffPermission(xPlayer) then
        return
    end

    TriggerClientEvent('sg_staff:receiveLogs', source, staffLogs)
end)

-- Toggle names
RegisterServerEvent('sg_staff:toggleNames')
AddEventHandler('sg_staff:toggleNames', function(enabled)
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canUseNames') then
        return
    end

    if enabled then
        playersWithNames[source] = true
        addLog('NAMES', string.format('%s enabled names display', xPlayer.getName()), xPlayer.getName(), nil)
    else
        playersWithNames[source] = nil
        addLog('NAMES', string.format('%s disabled names display', xPlayer.getName()), xPlayer.getName(), nil)
    end

    -- Broadcast updated staff members to all staff
    local xPlayers = ESX.GetPlayers()
    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer and hasStaffPermission(targetPlayer) then
            TriggerServerEvent('sg_staff:getStaffMembers')
            TriggerServerEvent('sg_staff:getPlayersWithNames')
        end
    end
end)

-- Kick player
RegisterServerEvent('sg_staff:kickPlayer')
AddEventHandler('sg_staff:kickPlayer', function(targetId, reason)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasPermission(xPlayer, 'canKick') then
        return
    end

    if not targetPlayer then
        return
    end

    local staffName = xPlayer.getName()
    local targetName = targetPlayer.getName()

    DropPlayer(targetId, string.format('[SAPPHIRE GAMING] Kicked by %s. Reason: %s', staffName, reason))
    addLog('KICK', string.format('%s kicked %s. Reason: %s', staffName, targetName, reason), staffName, targetName)
end)

-- Enhanced ban system with account ID integration
RegisterServerEvent('sg_staff:banPlayer')
AddEventHandler('sg_staff:banPlayer', function(targetId, reason, duration)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canBan') then
        return
    end

    if not targetData then
        return
    end

    local isPermanent = duration == 0 or duration >= *********
    local expiresAt = isPermanent and nil or os.date('%Y-%m-%d %H:%M:%S', os.time() + duration)

    -- Insert ban into database
    MySQL.insert('INSERT INTO sg_staff_bans (player_id, name, identifier, license, discord, ip, reason, banned_by_player_id, banned_by_name, expires_at, is_permanent) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
        targetData.playerId,
        targetData.name,
        targetData.identifier,
        targetData.license,
        targetData.discord,
        targetData.ip,
        reason,
        staffData.playerId,
        staffData.name,
        expiresAt,
        isPermanent and 1 or 0
    })

    -- Log the action
    addLogWithPlayerId('BAN', source, targetId, string.format('%s banned %s for %s. Reason: %s', staffData.name, targetData.name, isPermanent and 'permanent' or string.format('%d seconds', duration), reason), reason)

    -- Discord webhook
    sendDiscordLog('Player Banned', string.format('**%s** has been banned', targetData.name), Config.Discord.colors.ban, {
        { name = 'Banned Player', value = string.format('%s (ID: %d)', targetData.name, targetId), inline = true },
        { name = 'Banned By', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Reason', value = reason, inline = false },
        { name = 'Duration', value = isPermanent and 'Permanent' or string.format('%d seconds', duration), inline = true },
        { name = 'Player ID', value = tostring(targetData.playerId), inline = true }
    })

    -- Drop the player
    DropPlayer(targetId, string.format('[SAPPHIRE GAMING] You have been banned by %s.\nReason: %s\nDuration: %s\n\nAppeal at: discord.gg/sapphiregaming', staffData.name, reason, isPermanent and 'Permanent' or string.format('%d seconds', duration)))
end)

-- Check ban on player connecting
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    local identifiers = GetPlayerIdentifiers(source)
    local license = nil
    local discord = nil

    for _, id in pairs(identifiers) do
        if string.find(id, 'license:') then
            license = id
        elseif string.find(id, 'discord:') then
            discord = id
        end
    end

    if not license then
        setKickReason('No license found. Please restart FiveM and try again.')
        CancelEvent()
        return
    end

    -- Check for active bans
    local result = MySQL.query.await('SELECT * FROM sg_staff_bans WHERE (license = ? OR discord = ?) AND is_active = 1', {license, discord})

    if result and result[1] then
        local ban = result[1]

        -- Check if ban has expired
        if not ban.is_permanent and ban.expires_at then
            local expiresAt = os.time({
                year = tonumber(string.sub(ban.expires_at, 1, 4)),
                month = tonumber(string.sub(ban.expires_at, 6, 7)),
                day = tonumber(string.sub(ban.expires_at, 9, 10)),
                hour = tonumber(string.sub(ban.expires_at, 12, 13)),
                min = tonumber(string.sub(ban.expires_at, 15, 16)),
                sec = tonumber(string.sub(ban.expires_at, 18, 19))
            })

            if os.time() >= expiresAt then
                -- Ban has expired, deactivate it
                MySQL.update('UPDATE sg_staff_bans SET is_active = 0 WHERE id = ?', {ban.id})
                return
            end
        end

        -- Player is banned
        local banMessage = string.format([[
[SAPPHIRE GAMING] You are banned from this server.

Banned By: %s
Reason: %s
Duration: %s
Banned At: %s

Appeal at: discord.gg/sapphiregaming
Ban ID: #%d
        ]], ban.banned_by_name, ban.reason, ban.is_permanent == 1 and 'Permanent' or ban.expires_at, ban.banned_at, ban.id)

        setKickReason(banMessage)
        CancelEvent()
    end
end)

-- Unban player
RegisterServerEvent('sg_staff:unbanPlayer')
AddEventHandler('sg_staff:unbanPlayer', function(banId, reason)
    local source = source
    local staffData = getPlayerData(source)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canUnban') then
        return
    end

    -- Update ban record
    MySQL.update('UPDATE sg_staff_bans SET is_active = 0, unbanned_by_player_id = ?, unbanned_by_name = ?, unbanned_at = NOW(), unban_reason = ? WHERE id = ?', {
        staffData.playerId,
        staffData.name,
        reason,
        banId
    })

    -- Log the action
    addLogWithPlayerId('UNBAN', source, nil, string.format('%s unbanned ban ID #%d. Reason: %s', staffData.name, banId, reason), reason)

    -- Discord webhook
    sendDiscordLog('Player Unbanned', string.format('Ban ID #%d has been lifted', banId), Config.Discord.colors.unban, {
        { name = 'Unbanned By', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Reason', value = reason, inline = false },
        { name = 'Ban ID', value = tostring(banId), inline = true }
    })
end)

-- Warning system
RegisterServerEvent('sg_staff:warnPlayer')
AddEventHandler('sg_staff:warnPlayer', function(targetId, reason)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canWarn') then
        return
    end

    if not targetData then
        return
    end

    -- Insert warning into database
    MySQL.insert('INSERT INTO sg_staff_warnings (player_id, name, identifier, reason, warned_by_player_id, warned_by_name) VALUES (?, ?, ?, ?, ?, ?)', {
        targetData.playerId,
        targetData.name,
        targetData.identifier,
        reason,
        staffData.playerId,
        staffData.name
    })

    -- Log the action
    addLogWithPlayerId('WARN', source, targetId, string.format('%s warned %s. Reason: %s', staffData.name, targetData.name, reason), reason)

    -- Check if player should be auto-banned for too many warnings
    local warningCount = MySQL.query.await('SELECT COUNT(*) as count FROM sg_staff_warnings WHERE player_id = ? AND is_active = 1', {targetData.playerId})

    if warningCount and warningCount[1] and warningCount[1].count >= Config.Warnings.maxWarnings then
        -- Auto ban for too many warnings
        TriggerEvent('sg_staff:banPlayer', targetId, string.format('Auto-ban: %d warnings reached', Config.Warnings.maxWarnings), Config.Warnings.autoBanDuration)
    else
        -- Send warning message to player
        TriggerClientEvent('sg_staff:receiveWarning', targetId, reason, staffData.name)
    end

    -- Discord webhook
    sendDiscordLog('Player Warned', string.format('**%s** has been warned', targetData.name), Config.Discord.colors.warn, {
        { name = 'Warned Player', value = string.format('%s (ID: %d)', targetData.name, targetId), inline = true },
        { name = 'Warned By', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Reason', value = reason, inline = false },
        { name = 'Total Warnings', value = tostring((warningCount and warningCount[1] and warningCount[1].count or 0) + 1), inline = true }
    })
end)

-- Get player warnings
RegisterServerEvent('sg_staff:getPlayerWarnings')
AddEventHandler('sg_staff:getPlayerWarnings', function(targetId)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canCheckWarns') then
        return
    end

    if not targetData then
        return
    end

    local warnings = MySQL.query.await('SELECT * FROM sg_staff_warnings WHERE player_id = ? AND is_active = 1 ORDER BY warned_at DESC', {targetData.playerId})

    TriggerClientEvent('sg_staff:receivePlayerWarnings', source, warnings or {})
end)

-- Money management (919admin feature)
RegisterServerEvent('sg_staff:giveMoney')
AddEventHandler('sg_staff:giveMoney', function(targetId, accountType, amount)
    local source = source
    local staffData = getPlayerData(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canGiveTakeMoney') then
        return
    end

    if not targetPlayer then
        return
    end

    if amount > Config.Money.maxAmount then
        return
    end

    if accountType == 'money' then
        targetPlayer.addMoney(amount)
    elseif accountType == 'bank' then
        targetPlayer.addAccountMoney('bank', amount)
    elseif accountType == 'black_money' then
        targetPlayer.addAccountMoney('black_money', amount)
    end

    -- Log the transaction
    local targetData = getPlayerData(targetId)
    MySQL.insert('INSERT INTO sg_staff_money_logs (staff_player_id, staff_name, target_player_id, target_name, action, account_type, amount) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        staffData.playerId,
        staffData.name,
        targetData.playerId,
        targetData.name,
        'give',
        accountType,
        amount
    })

    addLogWithPlayerId('GIVE_MONEY', source, targetId, string.format('%s gave $%d (%s) to %s', staffData.name, amount, accountType, targetData.name), nil)

    -- Discord webhook
    sendDiscordLog('Money Given', string.format('**$%s** has been given to **%s**', ESX.Math.GroupDigits(amount), targetData.name), Config.Discord.colors.money, {
        { name = 'Staff Member', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Target Player', value = string.format('%s (ID: %d)', targetData.name, targetId), inline = true },
        { name = 'Amount', value = string.format('$%s', ESX.Math.GroupDigits(amount)), inline = true },
        { name = 'Account Type', value = accountType, inline = true }
    })
end)

-- Take money
RegisterServerEvent('sg_staff:takeMoney')
AddEventHandler('sg_staff:takeMoney', function(targetId, accountType, amount)
    local source = source
    local staffData = getPlayerData(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canGiveTakeMoney') then
        return
    end

    if not targetPlayer then
        return
    end

    if amount > Config.Money.maxAmount then
        return
    end

    local currentAmount = 0
    if accountType == 'money' then
        currentAmount = targetPlayer.getMoney()
        if currentAmount >= amount then
            targetPlayer.removeMoney(amount)
        else
            return
        end
    elseif accountType == 'bank' then
        currentAmount = targetPlayer.getAccount('bank').money
        if currentAmount >= amount then
            targetPlayer.removeAccountMoney('bank', amount)
        else
            return
        end
    elseif accountType == 'black_money' then
        currentAmount = targetPlayer.getAccount('black_money').money
        if currentAmount >= amount then
            targetPlayer.removeAccountMoney('black_money', amount)
        else
            return
        end
    end

    -- Log the transaction
    local targetData = getPlayerData(targetId)
    MySQL.insert('INSERT INTO sg_staff_money_logs (staff_player_id, staff_name, target_player_id, target_name, action, account_type, amount) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        staffData.playerId,
        staffData.name,
        targetData.playerId,
        targetData.name,
        'take',
        accountType,
        amount
    })

    addLogWithPlayerId('TAKE_MONEY', source, targetId, string.format('%s took $%d (%s) from %s', staffData.name, amount, accountType, targetData.name), nil)
end)

-- Vehicle spawning (919admin feature)
RegisterServerEvent('sg_staff:spawnVehicle')
AddEventHandler('sg_staff:spawnVehicle', function(targetId, vehicleModel)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canSpawnVehicle') then
        return
    end

    if not targetData then
        return
    end

    -- Log the vehicle spawn
    MySQL.insert('INSERT INTO sg_staff_vehicle_spawns (staff_player_id, staff_name, target_player_id, target_name, vehicle_model) VALUES (?, ?, ?, ?, ?)', {
        staffData.playerId,
        staffData.name,
        targetData.playerId,
        targetData.name,
        vehicleModel
    })

    addLogWithPlayerId('SPAWN_VEHICLE', source, targetId, string.format('%s spawned vehicle %s for %s', staffData.name, vehicleModel, targetData.name), nil)

    -- Trigger client to spawn vehicle
    TriggerClientEvent('sg_staff:spawnVehicleClient', targetId, vehicleModel)

    -- Discord webhook
    sendDiscordLog('Vehicle Spawned', string.format('**%s** spawned for **%s**', vehicleModel, targetData.name), Config.Discord.colors.vehicle, {
        { name = 'Staff Member', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Target Player', value = string.format('%s (ID: %d)', targetData.name, targetId), inline = true },
        { name = 'Vehicle Model', value = vehicleModel, inline = true }
    })
end)

-- Item management (919admin feature)
RegisterServerEvent('sg_staff:giveItem')
AddEventHandler('sg_staff:giveItem', function(targetId, itemName, quantity)
    local source = source
    local staffData = getPlayerData(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canGiveItem') then
        return
    end

    if not targetPlayer then
        return
    end

    if quantity > Config.Items.maxQuantity then
        return
    end

    -- Check if item is blacklisted
    for _, blacklistedItem in pairs(Config.Items.blacklistedItems) do
        if itemName == blacklistedItem then
            return
        end
    end

    -- Give item using ESX
    targetPlayer.addInventoryItem(itemName, quantity)

    -- Log the item transaction
    local targetData = getPlayerData(targetId)
    MySQL.insert('INSERT INTO sg_staff_item_logs (staff_player_id, staff_name, target_player_id, target_name, action, item_name, quantity) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        staffData.playerId,
        staffData.name,
        targetData.playerId,
        targetData.name,
        'give',
        itemName,
        quantity
    })

    addLogWithPlayerId('GIVE_ITEM', source, targetId, string.format('%s gave %dx %s to %s', staffData.name, quantity, itemName, targetData.name), nil)

    -- Discord webhook
    sendDiscordLog('Item Given', string.format('**%dx %s** given to **%s**', quantity, itemName, targetData.name), Config.Discord.colors.item, {
        { name = 'Staff Member', value = string.format('%s (ID: %d)', staffData.name, source), inline = true },
        { name = 'Target Player', value = string.format('%s (ID: %d)', targetData.name, targetId), inline = true },
        { name = 'Item', value = string.format('%dx %s', quantity, itemName), inline = true }
    })
end)

-- Reports system (919admin feature)
RegisterServerEvent('sg_staff:createReport')
AddEventHandler('sg_staff:createReport', function(subject, message)
    local source = source
    local playerData = getPlayerData(source)

    if not playerData then
        return
    end

    -- Insert report into database
    MySQL.insert('INSERT INTO sg_staff_reports (reporter_player_id, reporter_name, reporter_identifier, subject, message) VALUES (?, ?, ?, ?, ?)', {
        playerData.playerId,
        playerData.name,
        playerData.identifier,
        subject,
        message
    })

    -- Notify all staff
    local xPlayers = ESX.GetPlayers()
    for i = 1, #xPlayers do
        local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if xPlayer and allowedGroups[xPlayer.getGroup()] then
            TriggerClientEvent('sg_staff:newReport', xPlayer.source, {
                reporter = playerData.name,
                subject = subject,
                message = message
            })
        end
    end

    -- Discord webhook
    sendDiscordLog('New Report', string.format('**%s** submitted a new report', playerData.name), Config.Discord.colors.general, {
        { name = 'Reporter', value = string.format('%s (ID: %d)', playerData.name, source), inline = true },
        { name = 'Subject', value = subject, inline = false },
        { name = 'Message', value = message, inline = false }
    })
end)

-- Get reports
RegisterServerEvent('sg_staff:getReports')
AddEventHandler('sg_staff:getReports', function()
    local source = source
    local staffData = getPlayerData(source)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canViewReports') then
        return
    end

    local reports = MySQL.query.await('SELECT * FROM sg_staff_reports ORDER BY created_at DESC LIMIT 50')
    TriggerClientEvent('sg_staff:receiveReports', source, reports or {})
end)

-- Claim report
RegisterServerEvent('sg_staff:claimReport')
AddEventHandler('sg_staff:claimReport', function(reportId)
    local source = source
    local staffData = getPlayerData(source)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canClaimReport') then
        return
    end

    MySQL.update('UPDATE sg_staff_reports SET status = "claimed", claimed_by_player_id = ?, claimed_by_name = ?, claimed_at = NOW() WHERE id = ?', {
        staffData.playerId,
        staffData.name,
        reportId
    })

    addLogWithPlayerId('CLAIM_REPORT', source, nil, string.format('%s claimed report #%d', staffData.name, reportId), nil)
end)

-- Close report
RegisterServerEvent('sg_staff:closeReport')
AddEventHandler('sg_staff:closeReport', function(reportId, closeReason)
    local source = source
    local staffData = getPlayerData(source)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canDeleteReport') then
        return
    end

    MySQL.update('UPDATE sg_staff_reports SET status = "closed", closed_by_player_id = ?, closed_by_name = ?, closed_at = NOW(), close_reason = ? WHERE id = ?', {
        staffData.playerId,
        staffData.name,
        closeReason,
        reportId
    })

    addLogWithPlayerId('CLOSE_REPORT', source, nil, string.format('%s closed report #%d. Reason: %s', staffData.name, reportId, closeReason), closeReason)
end)

-- Spectate player (919admin feature)
RegisterServerEvent('sg_staff:spectatePlayer')
AddEventHandler('sg_staff:spectatePlayer', function(targetId)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canSpectate') then
        return
    end

    if not targetData then
        return
    end

    -- Check if trying to spectate another staff member
    if not Config.Spectate.allowSpectateStaff and allowedGroups[safeGetPlayer(targetId).getGroup()] then
        return
    end

    addLogWithPlayerId('SPECTATE', source, targetId, string.format('%s started spectating %s', staffData.name, targetData.name), nil)

    TriggerClientEvent('sg_staff:startSpectate', source, targetId)
end)

-- Stop spectating
RegisterServerEvent('sg_staff:stopSpectate')
AddEventHandler('sg_staff:stopSpectate', function()
    local source = source
    local staffData = getPlayerData(source)

    if not staffData then
        return
    end

    addLogWithPlayerId('STOP_SPECTATE', source, nil, string.format('%s stopped spectating', staffData.name), nil)

    TriggerClientEvent('sg_staff:stopSpectate', source)
end)

-- Screenshot player (919admin feature)
RegisterServerEvent('sg_staff:screenshotPlayer')
AddEventHandler('sg_staff:screenshotPlayer', function(targetId)
    local source = source
    local staffData = getPlayerData(source)
    local targetData = getPlayerData(targetId)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canScreenshot') then
        return
    end

    if not targetData then
        return
    end

    addLogWithPlayerId('SCREENSHOT', source, targetId, string.format('%s took screenshot of %s', staffData.name, targetData.name), nil)

    TriggerClientEvent('sg_staff:takeScreenshot', targetId, staffData.name)
end)

-- Get ox_inventory items for dropdown
RegisterServerEvent('sg_staff:getInventoryItems')
AddEventHandler('sg_staff:getInventoryItems', function()
    local source = source
    local staffData = getPlayerData(source)

    if not staffData or not hasPermission(safeGetPlayer(source), 'canGiveItem') then
        return
    end

    local items = {}

    print('[SG_STAFF] Loading items from ox_inventory data file...')

    -- First, verify the file exists
    local fileContent = LoadResourceFile('ox_inventory', 'data/items.lua')
    if not fileContent then
        print('[SG_STAFF] ERROR: Could not load ox_inventory/data/items.lua file!')
        print('[SG_STAFF] Make sure ox_inventory resource exists and has data/items.lua')
    else
        print('[SG_STAFF] Successfully loaded items.lua file, size:', string.len(fileContent), 'characters')
    end

    -- Method 1: Direct file loading from ox_inventory data folder
    local success, oxItems = pcall(function()
        return load(LoadResourceFile('ox_inventory', 'data/items.lua'))()
    end)

    if success and oxItems and type(oxItems) == 'table' then
        print('[SG_STAFF] Successfully loaded ox_inventory items file')
        local itemCount = 0

        for itemName, itemData in pairs(oxItems) do
            if itemData and itemData.label and type(itemData.label) == 'string' then
                table.insert(items, {
                    name = itemName,
                    label = itemData.label,
                    weight = itemData.weight or 0,
                    stack = itemData.stack ~= false, -- Default to true unless explicitly false
                    close = itemData.close or false
                })
                itemCount = itemCount + 1
            end
        end

        print('[SG_STAFF] Processed', itemCount, 'items from ox_inventory data file')

        -- Sort items alphabetically by label
        table.sort(items, function(a, b)
            return a.label < b.label
        end)

    else
        print('[SG_STAFF] Failed to load ox_inventory data file, trying export method')

        -- Method 2: Try ox_inventory export as fallback
        if GetResourceState('ox_inventory') == 'started' then
            local success2, oxItems2 = pcall(function()
                return exports.ox_inventory:Items()
            end)

            if success2 and oxItems2 and type(oxItems2) == 'table' then
                print('[SG_STAFF] Got items from ox_inventory export')
                for itemName, itemData in pairs(oxItems2) do
                    if itemData and itemData.label and type(itemData.label) == 'string' then
                        table.insert(items, {
                            name = itemName,
                            label = itemData.label,
                            weight = itemData.weight or 0,
                            stack = itemData.stack ~= false,
                            close = itemData.close or false
                        })
                    end
                end

                -- Sort items alphabetically by label
                table.sort(items, function(a, b)
                    return a.label < b.label
                end)
            else
                print('[SG_STAFF] Export method failed, trying database')

                -- Method 3: Database fallback
                local result = MySQL.query.await('SELECT name, label, weight FROM items ORDER BY label ASC')

                if result and #result > 0 then
                    print('[SG_STAFF] Got items from database:', #result)
                    for _, item in pairs(result) do
                        table.insert(items, {
                            name = item.name,
                            label = item.label or item.name,
                            weight = item.weight or 0,
                            stack = true,
                            close = false
                        })
                    end
                end
            end
        end
    end

    -- Final fallback if no items found
    if #items == 0 then
        print('[SG_STAFF] No items found anywhere, using fallback items')
        items = {
            {name = 'testburger', label = 'Test Burger', weight = 220, stack = true, close = false},
            {name = 'burger', label = 'Burger', weight = 220, stack = true, close = false},
            {name = 'sprunk', label = 'Sprunk', weight = 350, stack = true, close = false},
            {name = 'water', label = 'Water', weight = 500, stack = true, close = false},
            {name = 'phone', label = 'Phone', weight = 190, stack = false, close = false},
            {name = 'lockpick', label = 'Lockpick', weight = 160, stack = true, close = false},
            {name = 'money', label = 'Money', weight = 0, stack = true, close = false},
            {name = 'mustard', label = 'Mustard', weight = 500, stack = true, close = false},
            {name = 'parachute', label = 'Parachute', weight = 8000, stack = false, close = false},
            {name = 'garbage', label = 'Garbage', weight = 100, stack = true, close = false},
            {name = 'paperbag', label = 'Paper Bag', weight = 1, stack = false, close = false},
            {name = 'identification', label = 'Identification', weight = 50, stack = false, close = false},
            {name = 'panties', label = 'Knickers', weight = 10, stack = true, close = false}
        }
    end

    print('[SG_STAFF] Sending', #items, 'items to client')
    TriggerClientEvent('sg_staff:receiveInventoryItems', source, items)
end)

-- Teleport to player
RegisterServerEvent('sg_staff:teleportToPlayer')
AddEventHandler('sg_staff:teleportToPlayer', function(targetId)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasPermission(xPlayer, 'canTeleport') then
        return
    end

    if not targetPlayer then
        return
    end

    local targetPed = GetPlayerPed(targetId)
    local targetCoords = GetEntityCoords(targetPed)

    xPlayer.setCoords({x = targetCoords.x, y = targetCoords.y, z = targetCoords.z})
    addLog('TELEPORT', string.format('%s teleported to %s', xPlayer.getName(), targetPlayer.getName()), xPlayer.getName(), targetPlayer.getName())
end)

-- Bring player
RegisterServerEvent('sg_staff:bringPlayer')
AddEventHandler('sg_staff:bringPlayer', function(targetId)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasPermission(xPlayer, 'canBring') then
        return
    end

    if not targetPlayer then
        return
    end

    local staffPed = GetPlayerPed(source)
    local staffCoords = GetEntityCoords(staffPed)

    targetPlayer.setCoords({x = staffCoords.x, y = staffCoords.y, z = staffCoords.z})
    addLog('BRING', string.format('%s brought %s', xPlayer.getName(), targetPlayer.getName()), xPlayer.getName(), targetPlayer.getName())
end)

-- Kill all players (Admin+ only)
RegisterServerEvent('sg_staff:killAllPlayers')
AddEventHandler('sg_staff:killAllPlayers', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canKillAll') then
        print(string.format('[SAPPHIRE GAMING STAFF PANEL] Player %s attempted to use Kill All without permission', xPlayer.getName()))
        return
    end

    print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s is executing Kill All', xPlayer.getName()))

    local xPlayers = ESX.GetPlayers()
    local killedCount = 0

    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer then
            TriggerClientEvent('sg_staff:forceKill', targetPlayer.source)
            killedCount = killedCount + 1
        end
    end

    addLog('KILL_ALL', string.format('%s killed all players (%d players affected)', xPlayer.getName(), killedCount), xPlayer.getName(), nil)
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Kill All completed - %d players killed', killedCount))
end)

-- Heal/Revive all players (Admin+ only)
RegisterServerEvent('sg_staff:reviveAllPlayers')
AddEventHandler('sg_staff:reviveAllPlayers', function()
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canReviveAll') then
        print(string.format('[SAPPHIRE GAMING STAFF PANEL] Player %s attempted to use Heal All without permission', xPlayer.getName()))
        return
    end

    print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s is executing Heal All', xPlayer.getName()))

    local xPlayers = ESX.GetPlayers()
    local healedCount = 0

    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer then
            local targetPed = GetPlayerPed(targetPlayer.source)
            if IsPedDeadOrDying(targetPed, true) then
                TriggerClientEvent('sg_staff:forceRevive', targetPlayer.source)
            else
                TriggerClientEvent('sg_staff:forceHeal', targetPlayer.source)
            end
            healedCount = healedCount + 1
        end
    end

    addLog('HEAL_ALL', string.format('%s healed all players (%d players affected)', xPlayer.getName(), healedCount), xPlayer.getName(), nil)
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Heal All completed - %d players healed', healedCount))
end)

-- Revive players in radius (Senior Admin+ only)
RegisterServerEvent('sg_staff:revivePlayersRadius')
AddEventHandler('sg_staff:revivePlayersRadius', function(radius)
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canReviveAll') then
        print(string.format('[SAPPHIRE GAMING STAFF PANEL] Player %s attempted to use Revive Radius without permission', xPlayer.getName()))
        return
    end

    -- Validate radius
    radius = tonumber(radius) or Config.RadiusActions.defaultRadius
    if radius > Config.RadiusActions.maxRadius then
        radius = Config.RadiusActions.maxRadius
    end

    print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s is executing Revive Radius (%.1fm)', xPlayer.getName(), radius))

    local staffCoords = GetEntityCoords(GetPlayerPed(source))
    local xPlayers = ESX.GetPlayers()
    local healedCount = 0

    for i = 1, #xPlayers do
        local targetPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if targetPlayer then
            local targetCoords = GetEntityCoords(GetPlayerPed(targetPlayer.source))
            local distance = #(staffCoords - targetCoords)

            if distance <= radius then
                local targetPed = GetPlayerPed(targetPlayer.source)
                if IsPedDeadOrDying(targetPed, true) then
                    TriggerClientEvent('sg_staff:forceRevive', targetPlayer.source)
                else
                    TriggerClientEvent('sg_staff:forceHeal', targetPlayer.source)
                end
                healedCount = healedCount + 1
            end
        end
    end

    addLogWithPlayerId('REVIVE_RADIUS', source, nil, string.format('%s revived players in %.1fm radius (%d players affected)', xPlayer.getName(), radius, healedCount), nil)
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Revive Radius completed - %d players healed in %.1fm radius', healedCount, radius))
end)

-- Delete vehicles in radius (Senior Admin+ only)
RegisterServerEvent('sg_staff:deleteVehiclesRadius')
AddEventHandler('sg_staff:deleteVehiclesRadius', function(radius)
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canDeleteVehiclesRadius') then
        print(string.format('[SAPPHIRE GAMING STAFF PANEL] Player %s attempted to use Delete Vehicles Radius without permission', xPlayer.getName()))
        return
    end

    -- Validate radius
    radius = tonumber(radius) or Config.RadiusActions.defaultRadius
    if radius > Config.RadiusActions.maxRadius then
        radius = Config.RadiusActions.maxRadius
    end

    print(string.format('[SAPPHIRE GAMING STAFF PANEL] %s is executing Delete Vehicles Radius (%.1fm)', xPlayer.getName(), radius))

    local staffCoords = GetEntityCoords(GetPlayerPed(source))
    local deletedCount = 0

    -- Get all vehicles in the world
    local vehicles = GetAllVehicles()

    for i = 1, #vehicles do
        local vehicle = vehicles[i]
        if DoesEntityExist(vehicle) then
            local vehicleCoords = GetEntityCoords(vehicle)
            local distance = #(staffCoords - vehicleCoords)

            if distance <= radius then
                DeleteEntity(vehicle)
                deletedCount = deletedCount + 1
            end
        end
    end

    addLogWithPlayerId('DELETE_VEHICLES_RADIUS', source, nil, string.format('%s deleted vehicles in %.1fm radius (%d vehicles deleted)', xPlayer.getName(), radius, deletedCount), nil)
    print(string.format('[SAPPHIRE GAMING STAFF PANEL] Delete Vehicles Radius completed - %d vehicles deleted in %.1fm radius', deletedCount, radius))
end)

-- Set server time (Admin+ only)
RegisterServerEvent('sg_staff:setServerTime')
AddEventHandler('sg_staff:setServerTime', function(hour, minute)
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canChangeTime') then
        return
    end

    -- Validate time
    if hour < 0 or hour > 23 or minute < 0 or minute > 59 then
        return
    end

    -- Set time for all players
    TriggerClientEvent('sg_staff:syncTime', -1, hour, minute)
    addLog('TIME_CHANGE', string.format('%s changed server time to %02d:%02d', xPlayer.getName(), hour, minute), xPlayer.getName(), nil)
end)

-- Set server weather (Admin+ only)
RegisterServerEvent('sg_staff:setServerWeather')
AddEventHandler('sg_staff:setServerWeather', function(weather)
    local source = source
    local xPlayer = safeGetPlayer(source)

    if not xPlayer or not hasPermission(xPlayer, 'canChangeWeather') then
        return
    end

    -- Validate weather
    local validWeathers = {
        'CLEAR', 'EXTRASUNNY', 'CLOUDS', 'OVERCAST', 'RAIN', 'CLEARING',
        'THUNDER', 'SMOG', 'FOGGY', 'XMAS', 'SNOWLIGHT', 'BLIZZARD'
    }

    local isValid = false
    for _, validWeather in ipairs(validWeathers) do
        if weather == validWeather then
            isValid = true
            break
        end
    end

    if not isValid then
        return
    end

    -- Set weather for all players
    TriggerClientEvent('sg_staff:syncWeather', -1, weather)
    addLog('WEATHER_CHANGE', string.format('%s changed server weather to %s', xPlayer.getName(), weather), xPlayer.getName(), nil)
end)

-- TX Bans integration (if available)
RegisterServerEvent('sg_staff:txBansKick')
AddEventHandler('sg_staff:txBansKick', function(targetId, reason)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasPermission(xPlayer, 'canKick') then
        return
    end

    if not targetPlayer then
        return
    end

    -- Try to use txBans if available
    if GetResourceState('txBans') == 'started' then
        exports.txBans:kickPlayer(targetId, reason, xPlayer.getName())
    else
        -- Fallback to regular kick
        DropPlayer(targetId, string.format('[SAPPHIRE GAMING] Kicked by %s. Reason: %s', xPlayer.getName(), reason))
    end

    addLog('KICK', string.format('%s kicked %s. Reason: %s', xPlayer.getName(), targetPlayer.getName(), reason), xPlayer.getName(), targetPlayer.getName())
end)

-- TX Bans ban integration
RegisterServerEvent('sg_staff:txBansBan')
AddEventHandler('sg_staff:txBansBan', function(targetId, reason, duration)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasPermission(xPlayer, 'canBan') then
        return
    end

    if not targetPlayer then
        return
    end

    -- Try to use txBans if available
    if GetResourceState('txBans') == 'started' then
        exports.txBans:banPlayer(targetId, reason, duration, xPlayer.getName())
    else
        -- Fallback to regular ban
        DropPlayer(targetId, string.format('[SAPPHIRE GAMING] Banned by %s for %d seconds. Reason: %s', xPlayer.getName(), duration, reason))
    end

    addLog('BAN', string.format('%s banned %s for %d seconds. Reason: %s', xPlayer.getName(), targetPlayer.getName(), duration, reason), xPlayer.getName(), targetPlayer.getName())
end)

-- OX Inventory integration
RegisterServerEvent('sg_staff:clearInventory')
AddEventHandler('sg_staff:clearInventory', function(targetId)
    local source = source
    local xPlayer = safeGetPlayer(source)
    local targetPlayer = safeGetPlayer(targetId)

    if not xPlayer or not hasStaffPermission(xPlayer) then
        return
    end

    if not targetPlayer then
        return
    end

    -- Try to use ox_inventory if available
    if GetResourceState('ox_inventory') == 'started' then
        exports.ox_inventory:ClearInventory(targetPlayer.source)
        addLog('CLEAR_INV', string.format('%s cleared %s\'s inventory (OX)', xPlayer.getName(), targetPlayer.getName()), xPlayer.getName(), targetPlayer.getName())
    else
        -- Fallback message
        addLog('CLEAR_INV', string.format('%s attempted to clear %s\'s inventory (OX not available)', xPlayer.getName(), targetPlayer.getName()), xPlayer.getName(), targetPlayer.getName())
    end
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function(reason)
    local source = source
    if playersWithNames[source] then
        playersWithNames[source] = nil
    end
end)

-- Debug command to test item loading
RegisterCommand('testoxitems', function(source, args, rawCommand)
    local xPlayer = safeGetPlayer(source)
    if not xPlayer or not allowedGroups[xPlayer.getGroup()] then
        return
    end

    print('[SG_STAFF] Testing ox_inventory items loading methods...')

    -- Test Method 1: Direct file loading
    print('[SG_STAFF] Testing direct file loading...')
    local success1, oxItems1 = pcall(function()
        return load(LoadResourceFile('ox_inventory', 'data/items.lua'))()
    end)

    if success1 and oxItems1 then
        print('[SG_STAFF] Direct file loading SUCCESS, type:', type(oxItems1))
        local count = 0
        for k, v in pairs(oxItems1) do
            count = count + 1
            if count <= 5 then
                print('[SG_STAFF] File Item:', k, 'Label:', v.label or 'NO LABEL', 'Weight:', v.weight or 0)
            end
        end
        print('[SG_STAFF] Total items from file:', count)
    else
        print('[SG_STAFF] Direct file loading FAILED')
    end

    -- Test Method 2: Export method
    if GetResourceState('ox_inventory') == 'started' then
        print('[SG_STAFF] Testing export method...')
        local success2, oxItems2 = pcall(function()
            return exports.ox_inventory:Items()
        end)

        if success2 and oxItems2 then
            print('[SG_STAFF] Export method SUCCESS, type:', type(oxItems2))
            local count = 0
            for k, v in pairs(oxItems2) do
                count = count + 1
                if count <= 5 then
                    print('[SG_STAFF] Export Item:', k, 'Label:', v.label or 'NO LABEL')
                end
            end
            print('[SG_STAFF] Total items from export:', count)
        else
            print('[SG_STAFF] Export method FAILED')
        end
    else
        print('[SG_STAFF] ox_inventory is not started')
    end

    -- Test Method 3: Database
    print('[SG_STAFF] Testing database method...')
    local result = MySQL.query.await('SELECT name, label, weight FROM items LIMIT 5')
    if result then
        print('[SG_STAFF] Database query SUCCESS, items:', #result)
        for _, item in pairs(result) do
            print('[SG_STAFF] DB Item:', item.name, 'Label:', item.label)
        end
    else
        print('[SG_STAFF] Database query FAILED')
    end

    print('[SG_STAFF] Item loading test complete!')
end, false)

-- Clear cache when players join/leave for fresh data
AddEventHandler('playerConnecting', function()
    clearPlayerCache()
end)

AddEventHandler('playerDropped', function()
    clearPlayerCache()
end)

-- Clear cache when ESX player data changes
AddEventHandler('esx:playerLoaded', function()
    clearPlayerCache()
end)

print('^2[Sapphire Gaming Staff Panel]^0 Resource loaded successfully!')
print('^3[Sapphire Gaming Staff Panel]^0 Performance optimizations enabled!')
