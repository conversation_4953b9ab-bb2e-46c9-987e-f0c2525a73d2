# seat-switcher - 1.1.0
This is a very simple client-side script that is a modern version of the "[Disable Seat Shuffling](https://forum.cfx.re/t/release-disable-seat-shuffling/53526)" plugin by _callmejaf_.

This release fixes some of the most common bugs with it, such as:
- The seat shuffle animation no longer loops / does partial animations
- Door closing animation works properly now

## Commands

### `/seat [seat]`
This command will switch your character's seat to the desired seat.

### `/shuffle` (or `/shuff`)
This command lets the player shuffle to the driver seat.

## Changelog

### 1.1.0
- Added `/shuffle` as a command alias
- Use a better method of preventing seat shuffling

### 1.0.0
- Initial version


## License
This script is licensed under the MIT license. Have fun. The license details are available in the **LICENSE** file.

