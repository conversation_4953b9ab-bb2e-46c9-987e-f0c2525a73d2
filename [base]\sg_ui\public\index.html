<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
  <title>Sapphire Gaming UI - Enhanced</title>
  <style>
    * { box-sizing: border-box; }

    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      font-family: 'Inter', sans-serif;
      background: transparent;
    }

    /* In-game health bar */
    #player_hud {
      position: absolute;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .player_bar {
      position: absolute;
      left: 1.3%;
      top: 96.85%;
      width: 14.2%;
      height: 2.7%;
      transition: all 0.3s ease;
    }

    .health {
      width: 100%;
      height: 100%;
      background: rgba(17, 0, 255, 1);
      border-radius: 8px;
      overflow: hidden;
      position: relative;
      border: 0.5px solid #000000;
      display: flex;
    }

    .health-section {
      position: relative;
      height: 100%;
      overflow: hidden;
    }

    .health-main {
      width: 75%;
      background: rgba(17, 0, 255, 1);
    }

    .armor-section {
      width: 25%;
      background: rgb(1, 168, 29);
      border-left: 1px solid #000000;
      display: none; /* Hidden by default, shown when armor > 0 */
    }

    .armor {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
    }

    .health .bar {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: rgba(19, 164, 247, 1);
      transform-origin: left center;
      transform: scaleX(1);
      transition: transform 0.2s ease-out;
      z-index: 0;
    }

    .armor .bar {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      background: #1a6b35;
      transform-origin: left center;
      transform: scaleX(1);
      transition: transform 0.2s ease-out;
      z-index: 0;
    }

    .health .bar.pulse {
      animation: healthPulse 1.5s ease-in-out infinite;
    }

    .armor .bar.pulse {
      animation: armorPulse 1.5s ease-in-out infinite;
    }

    @keyframes healthPulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @keyframes armorPulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    .text {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 13px;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
      z-index: 2;
      white-space: nowrap;
      pointer-events: none;
    }

    .text i {
      margin-right: 0.5em;
      font-size: 14px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
    }

    .armor-text {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 11px;
      font-weight: bold;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
      z-index: 2;
      white-space: nowrap;
      pointer-events: none;
    }

    .armor-text i {
      margin-right: 0.3em;
      font-size: 12px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
    }

    /* Clean Setup UI - 50% Screen */
    #hudMenu {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      width: 50vw;
      height: 60vh;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.95);
      border: 2px solid #ffffff;
      border-radius: 12px;
      z-index: 10000;
      overflow-y: auto;
      animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
      to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }

    .menu-container {
      padding: 2rem;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .menu-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #333;
    }

    .menu-title {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .menu-title img {
      height: 40px;
      width: auto;
    }

    .menu-title h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: #ffffff;
    }

    .close-btn {
      width: 32px;
      height: 32px;
      background: transparent;
      border: 1px solid #666;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-size: 1.1rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .close-btn:hover {
      background: #333;
      border-color: #ffffff;
    }

    .menu-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid #333;
      border-radius: 8px;
      padding: 1.5rem;
    }

    .section-title {
      font-size: 1.1rem;
      font-weight: 500;
      color: #ffffff;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .section-title i {
      color: #ffffff;
    }

    /* Form Controls */
    .form-group {
      margin-bottom: 1rem;
    }

    .form-label {
      display: block;
      font-size: 0.9rem;
      font-weight: 500;
      color: #ffffff;
      margin-bottom: 0.5rem;
    }

    .form-control {
      width: 100%;
      padding: 0.5rem 0.75rem;
      background: #000000;
      border: 1px solid #333;
      border-radius: 6px;
      color: #ffffff;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #ffffff;
      background: #000000;
    }

    .form-control option {
      background: #000000;
      color: #ffffff;
      padding: 0.5rem;
    }

    .form-control option:hover {
      background: #333333;
      color: #ffffff;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .color-picker {
      width: 50px;
      height: 35px;
      border: 1px solid #333;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .color-picker:hover {
      border-color: #ffffff;
    }

    /* Buttons */
    .btn {
      padding: 0.6rem 1.2rem;
      border: 1px solid #333;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
      font-size: 0.9rem;
      background: transparent;
      color: #ffffff;
    }

    .btn:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: #ffffff;
    }

    .btn-primary {
      background: #ffffff;
      color: #000000;
      border-color: #ffffff;
    }

    .btn-primary:hover {
      background: #f0f0f0;
    }

    .btn-group {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
      justify-content: center;
    }
  </style>
</head>
<body>
  <!-- In-game health bar -->
  <div id="player_hud">
    <div class="player_bar">
      <div class="health">
        <div class="health-section health-main">
          <div class="bar" id="healthbar-fill"></div>
          <div class="text" id="healthbar-text">
            <i class="fa fa-heart"></i> Healthy
          </div>
        </div>
        <div class="health-section armor-section" id="armor-container">
          <div class="armor">
            <div class="bar" id="armorbar-fill"></div>
            <div class="armor-text" id="armorbar-text">
              <i class="fa fa-shield"></i> 0%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Clean Setup UI -->
  <div id="hudMenu">
    <div class="menu-container">
      <!-- Header -->
      <div class="menu-header">
        <div class="menu-title">
          <img src="img/logo.png" alt="Logo" />
          <h1>Sapphire Gaming UI</h1>
        </div>
        <button class="close-btn" onclick="exitHUD()">×</button>
      </div>

      <!-- Main Content -->
      <div class="menu-content">
        <!-- Color Settings -->
        <div class="section">
          <div class="section-title">
            <i class="fas fa-palette"></i>
            Color Settings
          </div>

          <div class="form-group">
            <label class="form-label">Health Bar Color</label>
            <div class="form-row">
              <input type="color" id="barColor" class="color-picker" value="#ff0000" onchange="updateColor()">
              <select id="colorPreset" class="form-control" onchange="applyColorPreset()">
                <option value="">Custom Color</option>
                <option value="classic">Classic Red</option>
                <option value="neon">Neon Green</option>
                <option value="ocean">Ocean Blue</option>
                <option value="sunset">Sunset Orange</option>
                <option value="purple">Royal Purple</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Background Color</label>
            <div class="form-row">
              <input type="color" id="bgColor" class="color-picker" value="#400303" onchange="updateBackgroundColor()">
              <select id="bgPreset" class="form-control" onchange="applyBgPreset()">
                <option value="">Custom Background</option>
                <option value="sapphire">Sapphire Blue (Default)</option>
                <option value="darkred">Dark Red</option>
                <option value="black">Black</option>
                <option value="darkgray">Dark Gray</option>
                <option value="transparent">Transparent</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Style Settings -->
        <div class="section">
          <div class="section-title">
            <i class="fas fa-cog"></i>
            Bar Style
          </div>

          <div class="form-group">
            <label class="form-label">Bar Style</label>
            <select id="barStyle" class="form-control" onchange="updateBarStyle()">
              <option value="rounded">Rounded</option>
              <option value="sharp">Sharp Corners</option>
              <option value="pill">Pill Shape</option>
            </select>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="btn-group">
          <button class="btn btn-primary" onclick="applySettings()">Apply Settings</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Health Bar Configuration
    let healthBarSettings = {
      color: 'rgba(19, 164, 247, 1)',
      backgroundColor: 'rgba(17, 0, 255, 1)',
      barStyle: 'rounded'
    };

    // Color presets
    const colorPresets = {
      classic: '#ff0000',
      neon: '#00ff00',
      ocean: '#0066cc',
      sunset: '#ff6600',
      purple: '#8b00ff'
    };

    // Background color presets
    const bgPresets = {
      sapphire: 'rgba(17, 0, 255, 1)',
      darkred: '#400303',
      black: '#000000',
      darkgray: '#333333',
      transparent: 'transparent'
    };

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', () => {
      loadSettings();
      applyCurrentSettings();

      // Initialize armor display as hidden
      const armorContainer = document.getElementById('armor-container');
      if (armorContainer) {
        armorContainer.style.display = 'none';
      }
    });

    // Load settings from localStorage
    function loadSettings() {
      const saved = localStorage.getItem('vhbSettings');
      if (saved) {
        healthBarSettings = { ...healthBarSettings, ...JSON.parse(saved) };
      }

      // Apply settings to form controls
      document.getElementById('barColor').value = healthBarSettings.color;
      document.getElementById('bgColor').value = healthBarSettings.backgroundColor;
      document.getElementById('barStyle').value = healthBarSettings.barStyle;
    }

    // Save settings to localStorage
    function saveSettings() {
      localStorage.setItem('vhbSettings', JSON.stringify(healthBarSettings));
    }

    // Update color when color picker changes
    function updateColor() {
      healthBarSettings.color = document.getElementById('barColor').value;
      saveSettings();
      applyCurrentSettings();
    }

    // Update background color when color picker changes
    function updateBackgroundColor() {
      healthBarSettings.backgroundColor = document.getElementById('bgColor').value;
      saveSettings();
      applyCurrentSettings();
    }

    // Apply color preset
    function applyColorPreset() {
      const preset = document.getElementById('colorPreset').value;
      if (preset && colorPresets[preset]) {
        document.getElementById('barColor').value = colorPresets[preset];
        healthBarSettings.color = colorPresets[preset];
        saveSettings();
        applyCurrentSettings();
      }
    }

    // Apply background color preset
    function applyBgPreset() {
      const preset = document.getElementById('bgPreset').value;
      if (preset && bgPresets[preset]) {
        document.getElementById('bgColor').value = bgPresets[preset];
        healthBarSettings.backgroundColor = bgPresets[preset];
        saveSettings();
        applyCurrentSettings();
      }
    }

    // Update bar style
    function updateBarStyle() {
      const style = document.getElementById('barStyle').value;
      healthBarSettings.barStyle = style;
      saveSettings();
      applyCurrentSettings();
    }

    // Apply current settings to the health bar
    function applyCurrentSettings() {
      const healthElement = document.querySelector('.health');
      const barElement = document.querySelector('.health .bar');
      const healthMainElement = document.querySelector('.health-main');
      const armorSectionElement = document.querySelector('.armor-section');

      if (healthElement && barElement) {
        // Apply bar style
        switch(healthBarSettings.barStyle) {
          case 'rounded':
            healthElement.style.borderRadius = '8px';
            break;
          case 'sharp':
            healthElement.style.borderRadius = '0px';
            break;
          case 'pill':
            healthElement.style.borderRadius = '50px';
            break;
        }

        // Apply colors to health bar
        barElement.style.backgroundColor = healthBarSettings.color;
        if (healthMainElement) {
          healthMainElement.style.backgroundColor = healthBarSettings.backgroundColor;
        }

        // Ensure black outline is always present (thin border)
        healthElement.style.border = '0.5px solid #000000';
      }
    }

    // Main action functions
    function applySettings() {
      // Settings are already saved automatically when changed
      applyCurrentSettings();
      exitHUD();
    }



    // Core functions
    function toggleHUD(show) {
      document.getElementById('hudMenu').style.display = show ? 'block' : 'none';
    }

    function exitHUD() {
      toggleHUD(false);
      fetch(`https://${GetParentResourceName()}/exitHUD`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
      setTimeout(() => { SetNuiFocus(false, false); }, 0);
    }

    // Message handling
    window.addEventListener('message', e => {
      if (e.data.action === 'updateHealth') {
        const fill = document.getElementById('healthbar-fill');
        const txt = document.getElementById('healthbar-text');
        const armorFill = document.getElementById('armorbar-fill');
        const armorTxt = document.getElementById('armorbar-text');
        const armorContainer = document.getElementById('armor-container');

        let pct = e.data.percent;
        let status = e.data.status;
        let armor = e.data.armor || 0;

        // Update health bar
        fill.style.transform = `scaleX(${pct / 100})`;
        fill.style.backgroundColor = healthBarSettings.color;
        txt.innerHTML = `<i class="fa fa-heart"></i> ${status}`;

        // Update armor bar and adjust health bar width
        const healthMain = document.querySelector('.health-main');
        if (armor > 0) {
          armorContainer.style.display = 'block';
          armorFill.style.transform = `scaleX(${armor / 100})`;
          armorTxt.innerHTML = `<i class="fa fa-shield"></i> ${armor}%`;
          // Adjust health bar to 75% width when armor is shown
          if (healthMain) healthMain.style.width = '75%';
        } else {
          armorContainer.style.display = 'none';
          // Health bar takes full width when no armor
          if (healthMain) healthMain.style.width = '100%';
        }

        // Ensure settings are applied
        applyCurrentSettings();
      }

      if (e.data.action === 'toggleHUDMenu') {
        toggleHUD(true);
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', e => {
      if (e.key === 'Escape') exitHUD();
    });
  </script>
</body>
</html>
